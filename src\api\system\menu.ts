// src/api/menu.ts
import request from '../request'

// 获取菜单列表
export const getMenuList = () => {
  return request({
    url: '/menu/list',
    method: 'get',
  })
}

// 获取菜单详情
export const getMenuDetail = (id: number) => {
  return request({
    url: `/menu/detail/${id}`,
    method: 'get',
  })
}

// 添加菜单
export const addMenu = (data: any) => {
  return request({
    url: '/menu/add',
    method: 'post',
    data,
  })
}

// 更新菜单
export const updateMenu = (data: any) => {
  return request({
    url: `/menu/update/${data.id}`,
    method: 'put',
    data,
  })
}

// 删除菜单
export const deleteMenu = (id: number) => {
  return request({
    url: `/menu/delete/${id}`,
    method: 'delete',
  })
}

// 获取菜单树
export const getMenuTree = () => {
  return request({
    url: '/menu/tree',
    method: 'get',
  })
}
