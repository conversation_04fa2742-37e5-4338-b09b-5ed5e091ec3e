/**
 * 树形表格拖拽工具函数
 * 专门用于处理可展开的树形表格拖拽排序
 */

import Sortable from 'sortablejs'
import type { SortableEvent } from 'sortablejs'

/**
 * 根据ID在树形数据中查找菜单项
 * @param treeData 树形数据数组
 * @param id 要查找的ID
 * @returns 找到的菜单项或null
 */
const findMenuItemById = (treeData: any[], id: string): any | null => {
  for (const item of treeData) {
    if (item.id.toString() === id) {
      return item
    }
    if (item.children && item.children.length > 0) {
      const found = findMenuItemById(item.children, id)
      if (found) {
        return found
      }
    }
  }
  return null
}

/**
 * 树形拖拽配置接口
 */
export interface TreeDragConfig {
  animation?: number
  ghostClass?: string
  chosenClass?: string
  dragClass?: string
  handle?: string
  treeData?: any[] // 树形数据
  onMoveValidate?: (draggedItem: any, targetItem: any) => boolean // 自定义验证函数
}

/**
 * 初始化树形表格拖拽功能
 * @param tableSelector 表格选择器字符串或DOM元素
 * @param onDragEnd 拖拽结束回调
 * @param config 拖拽配置
 * @returns Sortable实例
 */
export const initTreeTableDrag = (
  tableSelector: string | HTMLElement,
  onDragEnd: (oldIndex: number, newIndex: number) => void,
  config: TreeDragConfig = {},
): Sortable => {
  let tbody: HTMLElement | null

  try {
    if (typeof tableSelector === 'string') {
      const table = document.querySelector(tableSelector) as HTMLElement | null
      if (!table) {
        throw new Error(`未找到表格元素: ${tableSelector}`)
      }
      // 对于Element Plus表格，需要查找特定的tbody结构
      tbody = table.querySelector('.el-table__body-wrapper tbody') as HTMLElement | null
    } else {
      // 检查传入的元素是否已经是tbody
      if (tableSelector.tagName === 'TBODY') {
        tbody = tableSelector
      } else {
        // 对于Element Plus表格，需要查找特定的tbody结构
        tbody = tableSelector.querySelector('.el-table__body-wrapper tbody') as HTMLElement | null
      }
    }

    if (!tbody) {
      throw new Error('未找到表格tbody元素')
    }

    // 检查tbody是否有子元素
    if (tbody.children.length === 0) {
      console.warn('表格tbody没有子元素，拖拽功能可能无法正常工作')
    }

    const defaultConfig = {
      animation: 200,
      ghostClass: 'sortable-ghost',
      chosenClass: 'sortable-chosen',
      dragClass: 'sortable-drag',
      ...config,
    }

    // 创建Sortable实例，添加错误处理
    const sortable = new Sortable(tbody, {
      animation: defaultConfig.animation,
      ghostClass: defaultConfig.ghostClass,
      chosenClass: defaultConfig.chosenClass,
      dragClass: defaultConfig.dragClass,

      // 拖拽移动事件
      onMove: (evt: any) => {
        try {
          const draggedRow = evt.dragged as HTMLElement
          const targetRow = evt.related as HTMLElement
          
          if (!draggedRow || !targetRow) {
            return true
          }
          
          // 获取行的数据ID - 使用多种方式确保获取成功
          const getRowId = (row: HTMLElement | null): string | null => {
            if (!row) return null
            
            // 方式1: 直接获取 data-row-key 属性
            let rowId = row.getAttribute('data-row-key')
            if (rowId) return rowId
            
            // 方式2: 检查是否有其他可能的属性名
            const possibleAttrs = ['row-key', 'data-id', 'id']
            for (const attr of possibleAttrs) {
              rowId = row.getAttribute(attr)
              if (rowId) return rowId
            }
            
            // 方式3: 从 class 中提取（如果 Element Plus 使用 class 存储 ID）
            const classList = Array.from(row.classList)
            const idClass = classList.find(cls => cls.includes('row-') || cls.includes('id-'))
            if (idClass) {
              const match = idClass.match(/(?:row-|id-)(\d+)/)
              if (match) return match[1]
            }
            
            // 方式4: 从 dataset 中获取
            if (row.dataset && row.dataset.rowKey) {
              return row.dataset.rowKey
            }
            
            return null
          }
          
          const draggedId = getRowId(draggedRow)
          const targetId = getRowId(targetRow)
          
          if (!draggedId || !targetId) {
            console.warn('onMove中无法获取行数据ID', { 
              draggedId, 
              targetId,
              draggedRowAttributes: draggedRow ? Array.from(draggedRow.attributes).map(attr => `${attr.name}=${attr.value}`) : [],
              targetRowAttributes: targetRow ? Array.from(targetRow.attributes).map(attr => `${attr.name}=${attr.value}`) : []
            })
            return true
          }
          
          // 如果有自定义验证函数，优先使用
          if (config.onMoveValidate) {
            const draggedItem = findMenuItemById(config.treeData || [], draggedId)
            const targetItem = findMenuItemById(config.treeData || [], targetId)
            return config.onMoveValidate(draggedItem, targetItem)
          }
          
          // 允许所有拖拽操作
          return true
        } catch (error) {
          console.warn('拖拽移动验证失败:', error)
          return true // 验证失败时默认允许
        }
      },

      onEnd: (evt: SortableEvent) => {
        try {
          const { oldIndex, newIndex } = evt
          if (oldIndex !== undefined && newIndex !== undefined && oldIndex !== newIndex) {
            // 确保tbody存在
            if (!tbody) {
              console.warn('tbody元素不存在')
              return
            }

            // 获取tbody的所有直接子元素（tr）
            const tbodyChildren = Array.from(tbody.children)
            if (newIndex >= tbodyChildren.length) {
              console.warn('新索引超出tbody子元素范围:', { newIndex, totalChildren: tbodyChildren.length })
              return
            }

            // 获取拖拽的行元素
            const draggedRow = evt.item as HTMLElement
            const targetRow = tbodyChildren[newIndex] as HTMLElement
            
            if (!draggedRow || !targetRow) {
              console.warn('无法获取拖拽或目标行元素')
              return
            }
            
            // 获取行的数据ID - 使用多种方式确保获取成功
            const getRowId = (row: HTMLElement | null): string | null => {
              if (!row) return null
              
              // 方式1: 直接获取 data-row-key 属性
              let rowId = row.getAttribute('data-row-key')
              if (rowId) return rowId
              
              // 方式2: 检查是否有其他可能的属性名
              const possibleAttrs = ['row-key', 'data-id', 'id']
              for (const attr of possibleAttrs) {
                rowId = row.getAttribute(attr)
                if (rowId) return rowId
              }
              
              // 方式3: 从 class 中提取（如果 Element Plus 使用 class 存储 ID）
              const classList = Array.from(row.classList)
              const idClass = classList.find(cls => cls.includes('row-') || cls.includes('id-'))
              if (idClass) {
                const match = idClass.match(/(?:row-|id-)(\d+)/)
                if (match) return match[1]
              }
              
              // 方式4: 从 dataset 中获取
              if (row.dataset && row.dataset.rowKey) {
                return row.dataset.rowKey
              }
              
              return null
            }
            
            const draggedId = getRowId(draggedRow)
            const targetId = getRowId(targetRow)
            
            if (!draggedId || !targetId) {
              console.warn('无法获取行数据ID', { 
                draggedId, 
                targetId,
                draggedRowAttributes: draggedRow ? Array.from(draggedRow.attributes).map(attr => `${attr.name}=${attr.value}`) : [],
                targetRowAttributes: targetRow ? Array.from(targetRow.attributes).map(attr => `${attr.name}=${attr.value}`) : []
              })
              return
            }
            
            // 允许所有拖拽操作
            onDragEnd(oldIndex, newIndex)
          }
        } catch (error) {
          console.error('拖拽结束回调失败:', error)
        }
      },

      // 添加错误处理
      onStart: () => {
        try {
          // 拖拽开始时的处理
        } catch (error) {
          console.error('拖拽开始失败:', error)
        }
      },
    })

    return sortable
  } catch (error) {
    console.error('初始化树形表格拖拽功能失败:', error)
    throw error
  }
}

/**
 * 安全销毁拖拽实例
 * 彻底清理Sortable实例及其所有引用，避免内存泄漏和访问null对象错误
 * @param sortable Sortable实例
 */
export const destroyTreeTableDrag = (sortable: Sortable | null): void => {
  try {
    if (sortable) {
      // 检查sortable实例是否还有效
      if (!sortable.el) {
        console.warn('Sortable实例的el元素不存在，跳过销毁')
        return
      }
      
      // 检查destroy方法是否存在
      if (typeof sortable.destroy !== 'function') {
        console.warn('Sortable实例的destroy方法不可用')
        return
      }
      
      // 检查是否正在拖拽中
      if ((sortable as any)._dragStarted) {
        console.warn('Sortable实例正在拖拽中，跳过销毁以避免错误')
        return
      }
      
      // 移除所有事件监听器
      try {
        // 清理内部事件监听器
        const events = ['onStart', 'onEnd', 'onMove', 'onAdd', 'onRemove', 'onUpdate', 'onSort', 'onChoose', 'onUnchoose', 'onFilter', 'onClone']
        events.forEach(eventName => {
          if (sortable[eventName as keyof Sortable]) {
            delete sortable[eventName as keyof Sortable]
          }
        })
      } catch (eventError) {
        console.warn('清理事件监听器失败:', eventError)
      }
      
      // 清理DOM元素上的SortableJS相关属性
      try {
        if (sortable.el) {
          // 移除SortableJS添加的data属性
          const sortableAttrs = Array.from(sortable.el.attributes)
            .filter(attr => attr.name.startsWith('data-sortable'))
            .map(attr => attr.name)
          
          sortableAttrs.forEach(attr => {
            sortable.el.removeAttribute(attr)
          })
          
          // 移除拖拽相关的类
          sortable.el.classList.remove('sortable-ghost', 'sortable-chosen', 'sortable-drag', 'sortable-dragging')
        }
      } catch (domError) {
        console.warn('清理DOM属性失败:', domError)
      }
      
      // 使用try-catch包装destroy调用，防止SortableJS内部错误
      try {
        // 在销毁前，先禁用所有功能
        if (typeof (sortable as any).option === 'function') {
          (sortable as any).option('disabled', true)
        }
        
        // 等待一小段时间确保禁用生效
        setTimeout(() => {
          if (sortable && typeof sortable.destroy === 'function') {
            sortable.destroy()
          }
        }, 50)
        
      } catch (destroyError) {
        console.warn('Sortable实例销毁失败，可能是内部状态问题:', destroyError)
        // 即使销毁失败，也不抛出错误，让程序继续执行
      }
    }
  } catch (error) {
    console.error('销毁树形表格拖拽实例时发生错误:', error)
    // 静默处理错误，不中断程序执行
  }
}