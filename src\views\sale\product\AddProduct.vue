<template>
  <div class="add-product-container">
    <div class="page-header">
      <h2>添加商品</h2>
      <el-button @click="handleCancel">返回</el-button>
    </div>
    
    <div class="form-container">
      <el-form
        ref="addProductFormRef"
        :model="productForm"
        :rules="productRules"
        label-width="120px"
        class="product-form"
      >
        <el-form-item label="商品名称" prop="name">
          <el-input v-model="productForm.name" placeholder="请输入商品名称" />
        </el-form-item>
        
        <el-form-item label="商品分类" prop="categoryId">
          <el-tree-select
            v-model="productForm.categoryId"
            :data="categoryTreeOptions"
            :props="{ label: 'name', value: 'id', children: 'children' }"
            placeholder="请选择商品分类"
            check-strictly
            :render-after-expand="false"
          />
        </el-form-item>
        
        <el-form-item label="商品规格">
          <div class="spec-actions">
            <el-button type="primary" @click="handleAddSpec">
              <el-icon><Plus /></el-icon>
              添加规格
            </el-button>
          </div>
          
          <el-table :data="productSpecs" border stripe class="spec-table">
            <el-table-column label="规格名称*" width="140">
              <template #default="{ row, $index }">
                <el-input
                  v-if="row.isEditing"
                  v-model="row.name"
                  size="small"
                  placeholder="请输入规格名称"
                />
                <span v-else>{{ row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="单位*" width="90">
              <template #default="{ row }">
                <el-input
                  v-if="row.isEditing"
                  v-model="row.unit"
                  size="small"
                  placeholder="如：个、箱"
                />
                <span v-else>{{ row.unit || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="换算*" width="100">
              <template #default="{ row }">
                <el-input-number
                  v-if="row.isEditing"
                  v-model="row.conversionNum"
                  size="small"
                  :min="0"
                  style="width: 80px"
                />
                <span v-else>{{ row.conversionNum || 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column label="价格*" width="150">
              <template #default="{ row }">
                <el-input-number
                  v-if="row.isEditing"
                  v-model="row.price"
                  size="small"
                  :precision="4"
                  :min="0"
                  style="width: 100px"
                />
                <span v-else>¥{{ (row.price || 0).toFixed(4) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="重量" width="100">
              <template #default="{ row }">
                <el-input-number
                  v-if="row.isEditing"
                  v-model="row.weight"
                  size="small"
                  :precision="4"
                  :min="0"
                  style="width: 80px"
                />
                <span v-else>{{ (row.weight || 0).toFixed(4) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="条形码" width="120">
              <template #default="{ row }">
                <el-input
                  v-if="row.isEditing"
                  v-model="row.barcode"
                  size="small"
                  placeholder="请输入条形码"
                />
                <span v-else>{{ row.barcode || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="体积" width="80">
              <template #default="{ row }">
                <el-input-number
                  v-if="row.isEditing"
                  v-model="row.volume"
                  size="small"
                  :precision="4"
                  :min="0"
                  style="width: 70px"
                />
                <span v-else>{{ (row.volume || 0).toFixed(4) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="每箱数量" width="90">
              <template #default="{ row }">
                <el-input-number
                  v-if="row.isEditing"
                  v-model="row.perBoxNum"
                  size="small"
                  :min="0"
                  style="width: 80px"
                />
                <span v-else>{{ row.perBoxNum || 0 }}</span>
              </template>
            </el-table-column>
            <el-table-column label="警戒库存" width="90">
              <template #default="{ row }">
                <el-input-number
                  v-if="row.isEditing"
                  v-model="row.alertInventory"
                  size="small"
                  :min="0"
                  style="width: 80px"
                />
                <span v-else>{{ row.alertInventory || 0 }}</span>
              </template>
            </el-table-column>
            <el-table-column label="批量（采购/生产）数量" width="80">
              <template #default="{ row }">
                <el-input-number
                  v-if="row.isEditing"
                  v-model="row.batchNum"
                  size="small"
                  :min="0"
                  style="width: 70px"
                />
                <span v-else>{{ row.batchNum || 0 }}</span>
              </template>
            </el-table-column>
            <el-table-column label="默认" width="70" align="center">
              <template #default="{ row }">
                <el-switch
                  v-model="row.isDefault"
                  size="small"
                />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" fixed="right">
              <template #default="{ row, $index }">
                <div class="action-buttons">
                  <el-button size="small" type="danger" @click="handleDeleteSpec($index)">
                    删除
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            保存商品
          </el-button>
          <el-button @click="handleCancel">取消</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'

// 导入API
import {
  getCategoryTree,
  addProduct,
  type ProductCategory,
  type ProductAddDTO,
  type ProductSpecAddDTO,
} from '@/api/sale'

const router = useRouter()
const route = useRoute()

// 表单引用
const addProductFormRef = ref<FormInstance>()

// 提交状态
const submitting = ref(false)

// 商品表单数据
const productForm = reactive({
  name: '',
  categoryId: 0,
})

// 商品规格数据
const productSpecs = ref<any[]>([])

/**
 * 从localStorage恢复表单数据
 */
const restoreFormData = () => {
  try {
    const savedData = localStorage.getItem('addProductFormData')
    if (savedData) {
      const parsedData = JSON.parse(savedData)
      // 恢复商品表单数据
      if (parsedData.productForm) {
        productForm.name = parsedData.productForm.name || ''
        productForm.categoryId = parsedData.productForm.categoryId || 0
      }
      // 恢复商品规格数据
      if (parsedData.productSpecs && Array.isArray(parsedData.productSpecs)) {
        productSpecs.value = parsedData.productSpecs
      }
    }
  } catch (error) {
    console.error('恢复表单数据失败:', error)
  }
}

/**
 * 保存表单数据到localStorage
 */
const saveFormData = () => {
  try {
    const dataToSave = {
      productForm: {
        name: productForm.name,
        categoryId: productForm.categoryId
      },
      productSpecs: productSpecs.value
    }
    localStorage.setItem('addProductFormData', JSON.stringify(dataToSave))
  } catch (error) {
    console.error('保存表单数据失败:', error)
  }
}

/**
 * 清除localStorage中的表单数据
 */
const clearFormData = () => {
  try {
    localStorage.removeItem('addProductFormData')
  } catch (error) {
    console.error('清除表单数据失败:', error)
  }
}

/**
 * 处理页面关闭事件
 */
const handleBeforeUnload = () => {
  // 在页面关闭前清除表单数据
  clearFormData()
}

// 分类树数据
const categoryTreeData = ref<ProductCategory[]>([])

// 表单验证规则
const productRules: FormRules = {
  name: [{ required: true, message: '请输入商品名称', trigger: 'blur' }],
  categoryId: [{ required: true, message: '请选择商品分类', trigger: 'change' }],
}

// 计算属性：分类树选项
const categoryTreeOptions = computed(() => {
  const addRootOption = (data: ProductCategory[]): any[] => {
    const result: any[] = [{ id: 0, name: '请选择分类', children: [] }]
    data.forEach((item) => {
      result[0].children.push({
        id: item.id,
        name: item.name,
        children: item.children ? transformChildren(item.children) : [],
      })
    })
    return result
  }

  const transformChildren = (data: ProductCategory[]): any[] => {
    return data.map((item) => ({
      id: item.id,
      name: item.name,
      children: item.children ? transformChildren(item.children) : [],
    }))
  }

  return addRootOption(categoryTreeData.value)
})

/**
 * 加载分类树数据
 */
const loadCategoryTree = async () => {
  try {
    const response = await getCategoryTree()
    if (response.code === 1) {
      categoryTreeData.value = response.data
      // 设置默认分类
      setDefaultCategory()
    } else {
      ElMessage.error(response.msg || '获取分类树失败')
    }
  } catch (error: any) {
    ElMessage.error(error.message || '获取分类树失败')
  }
}

/**
 * 设置默认分类
 */
const setDefaultCategory = () => {
  const categoryId = route.query.categoryId
  if (categoryId && !isNaN(Number(categoryId))) {
    const categoryIdNum = Number(categoryId)
    // 检查分类是否存在
    const categoryExists = findCategoryInTree(categoryTreeData.value, categoryIdNum)
    if (categoryExists) {
      productForm.categoryId = categoryIdNum
    }
  }
}

/**
 * 在分类树中查找分类是否存在
 */
const findCategoryInTree = (categories: ProductCategory[], targetId: number): boolean => {
  for (const category of categories) {
    if (category.id === targetId) {
      return true
    }
    if (category.children && category.children.length > 0) {
      if (findCategoryInTree(category.children, targetId)) {
        return true
      }
    }
  }
  return false
}

/**
 * 添加商品规格
 */
const handleAddSpec = () => {
  const newSpec = {
    id: Date.now(),
    name: '',
    unit: '',
    conversionNum: 1,
    price: 0,
    barcode: '',
    volume: 0,
    weight: 0,
    perBoxNum: 0,
    alertInventory: 0,
    batchNum: 0,
    isDefault: productSpecs.value.length === 0, // 第一个规格默认为默认规格
    sort: productSpecs.value.length,
    isEditing: true,
    isNew: true,
  }
  productSpecs.value.push(newSpec)
  // 保存数据到localStorage
  saveFormData()
}

/**
 * 删除商品规格
 */
const handleDeleteSpec = (index: number) => {
  productSpecs.value.splice(index, 1)
  // 重新设置默认规格
  if (productSpecs.value.length > 0) {
    productSpecs.value[0].isDefault = true
  }
  // 保存数据到localStorage
  saveFormData()
}

/**
 * 提交表单
 */
const handleSubmit = async () => {
  try {
    // 验证表单
    await addProductFormRef.value?.validate()

    if (!productForm.name.trim()) {
      ElMessage.error('商品名称不能为空')
      return
    }

    submitting.value = true

    // 验证规格数据
    if (productSpecs.value.length > 0) {
      for (const spec of productSpecs.value) {
        if (!spec.name.trim()) {
          ElMessage.error('规格名称不能为空')
          return
        }
        if (!spec.unit.trim()) {
          ElMessage.error('规格单位不能为空')
          return
        }
        if (!spec.conversionNum || spec.conversionNum <= 0) {
          ElMessage.error('规格换算数量必须大于0')
          return
        }
        if (spec.price < 0) {
          ElMessage.error('规格价格不能小于0')
          return
        }
      }
    }

    // 准备规格数据
    const specsData: ProductSpecAddDTO[] = []
    if (productSpecs.value.length > 0) {
      for (const spec of productSpecs.value) {
        if (spec.name.trim()) {
          specsData.push({
            name: spec.name,
            unit: spec.unit || '',
            conversionNum: spec.conversionNum || 1,
            price: spec.price || 0,
            barcode: spec.barcode || '',
            volume: spec.volume || 0,
            weight: spec.weight || 0,
            perBoxNum: spec.perBoxNum || 0,
            alertInventory: spec.alertInventory || 0,
            batchNum: spec.batchNum || 0,
            isDefault: spec.isDefault || false,
          })
        }
      }
    }

    // 一次性提交商品和规格数据
    const productData: ProductAddDTO = {
      name: productForm.name,
      categoryId: productForm.categoryId || 0,
      specs: specsData.length > 0 ? specsData : undefined
    }
    const productResponse = await addProduct(productData)
    if (productResponse.code !== 1) {
      ElMessage.error(productResponse.msg || '添加商品失败')
      return
    }

    ElMessage.success('添加商品成功')
    
    // 清除保存的表单数据，但保留分类数据以便继续添加
    const currentCategoryId = productForm.categoryId
    clearFormData()
    // 恢复分类数据
    productForm.categoryId = currentCategoryId
    
    // 清空商品名称和规格数据，保留分类选择
    productForm.name = ''
    productSpecs.value = []
  } catch (error: any) {
    ElMessage.error(error.message || '添加商品失败')
  } finally {
    submitting.value = false
  }
}

/**
 * 取消操作
 */
const handleCancel = () => {
  // 清除保存的表单数据
  clearFormData()
  // 返回首页
  router.push('/dashboard')
}

// 组件挂载时加载数据
onMounted(() => {
  loadCategoryTree()
  // 恢复表单数据
  restoreFormData()
  // 监听页面关闭事件，清除数据
  window.addEventListener('beforeunload', handleBeforeUnload)
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  window.removeEventListener('beforeunload', handleBeforeUnload)
})

// 监听分类树数据变化，设置默认分类
watch(categoryTreeData, () => {
  setDefaultCategory()
})

// 监听商品表单数据变化，自动保存
watch(productForm, () => {
  saveFormData()
}, { deep: true })

// 监听商品规格数据变化，自动保存
watch(productSpecs, () => {
  saveFormData()
}, { deep: true })
</script>

<style scoped>
.add-product-container {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  min-height: calc(100vh - 120px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.page-header .el-button {
  padding: 8px 16px;
  font-size: 14px;
  border-radius: 6px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.form-container {
  width: 100%;
  padding: 0 20px;
  box-sizing: border-box;
}

.product-form {
  background: #f9f9f9;
  padding: 30px;
  border-radius: 8px;
  width: 100%;
  box-sizing: border-box;
}

.spec-actions {
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-start;
}

.spec-actions .el-button {
  padding: 10px 20px;
  font-size: 14px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.spec-table {
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.action-buttons .el-button {
  padding: 6px 12px;
  font-size: 12px;
  border-radius: 4px;
  min-width: 60px;
}

:deep(.el-form-item__content) {
  flex-wrap: nowrap;
}

/* 底部按钮组样式 */
:deep(.el-form-item:last-child) {
  margin-bottom: 0;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

:deep(.el-form-item:last-child .el-form-item__content) {
  display: flex;
  justify-content: center;
  gap: 16px;
}

:deep(.el-form-item:last-child .el-button) {
  padding: 12px 32px;
  font-size: 14px;
  border-radius: 6px;
  min-width: 100px;
}
</style>