<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElForm, ElFormItem, ElInput, ElButton, ElMessage } from 'element-plus'
import { User, Lock } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/counter'
import type { FormInstance } from 'element-plus'

const router = useRouter()
const userStore = useUserStore()

// 定义表单数据
const formData = ref({
  username: '',
  password: '',
  captcha: '',
})

// 登录加载状态
const loading = ref(false)

// 表单引用
const loginForm = ref<FormInstance>()

// 验证码相关
const captchaCode = ref('')

// 生成4位纯数字验证码
const generateCaptcha = () => {
  const chars = '0123456789'
  let result = ''
  for (let i = 0; i < 4; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  captchaCode.value = result
}

// 刷新验证码
const refreshCaptcha = () => {
  generateCaptcha()
  // 清空用户输入的验证码
  formData.value.captcha = ''
}

// 初始化验证码
generateCaptcha()

// 表单规则
const rules = {
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' },
  ],
  captcha: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: any) => {
        if (value.toLowerCase() !== captchaCode.value.toLowerCase()) {
          callback(new Error('验证码错误'))
        } else {
          callback()
        }
      },
      trigger: 'blur',
    },
  ],
}

// 提交表单
const submitForm = async () => {
  if (!loginForm.value) return

  try {
    const valid = await loginForm.value.validate()
    if (!valid) return

    loading.value = true
    const success = await userStore.userLogin(formData.value)

    if (success) {
      // 登录成功，跳转到首页
      router.push('/dashboard')
    } else {
      // 登录失败，刷新验证码
      refreshCaptcha()
    }
  } catch (error) {
    console.error('登录失败:', error)
    // 登录失败，刷新验证码
    refreshCaptcha()
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  loginForm.value?.resetFields()
}
</script>

<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <h2 class="title">企业管理系统</h2>
        <p class="subtitle">欢迎登录</p>
      </div>
      <el-form
        ref="loginForm"
        :model="formData"
        :rules="rules"
        label-width="0"
        class="login-form"
        @keyup.enter="submitForm"
      >
        <el-form-item prop="username">
          <el-input
            v-model="formData.username"
            placeholder="请输入用户名"
            size="large"
            :prefix-icon="User"
            clearable
          />
        </el-form-item>
        <el-form-item prop="password">
          <el-input
            v-model="formData.password"
            type="password"
            placeholder="请输入密码"
            size="large"
            :prefix-icon="Lock"
            show-password
            clearable
          />
        </el-form-item>

        <el-form-item prop="captcha">
          <div class="captcha-container">
            <el-input
              v-model="formData.captcha"
              placeholder="请输入验证码"
              size="large"
              maxlength="4"
              clearable
              style="flex: 1"
            />
            <div class="captcha-code" @click="refreshCaptcha" title="点击刷新验证码">
              {{ captchaCode }}
            </div>
          </div>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            size="large"
            style="width: 100%"
            :loading="loading"
            @click="submitForm"
          >
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="default" size="large" style="width: 100%" @click="resetForm">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  box-sizing: border-box;
}

.login-box {
  width: 100%;
  max-width: 420px;
  padding: 40px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
}

.title {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 28px;
  font-weight: 600;
}

.subtitle {
  margin: 0;
  color: #909399;
  font-size: 16px;
}

.login-form {
  width: 100%;
}

.login-form .el-form-item {
  margin-bottom: 24px;
}

.login-form .el-input {
  width: 100%;
}

.login-form .el-input__wrapper {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.login-form .el-input__wrapper:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.login-form .el-input.is-focus .el-input__wrapper {
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.login-form .el-button {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s;
}

.login-form .el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.login-form .el-button--primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
}

.login-form .el-button--default {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #606266;
}

.login-form .el-button--default:hover {
  background-color: #ecf5ff;
  border-color: #409eff;
  color: #409eff;
}

/* 验证码样式 */
.captcha-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.captcha-code {
  width: 100px;
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  font-weight: bold;
  letter-spacing: 2px;
  cursor: pointer;
  user-select: none;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.captcha-code:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-container {
    padding: 10px;
  }

  .login-box {
    padding: 30px 20px;
    max-width: 100%;
  }

  .title {
    font-size: 24px;
  }

  .subtitle {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .login-box {
    padding: 20px 15px;
  }

  .title {
    font-size: 20px;
  }

  .login-header {
    margin-bottom: 30px;
  }

  .login-form .el-form-item {
    margin-bottom: 20px;
  }
}

/* 动画效果 */
.login-box {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
