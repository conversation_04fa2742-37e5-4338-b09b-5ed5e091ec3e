<template>
  <div class="dashboard-container">
    <!-- 欢迎卡片 -->
    <el-card class="welcome-card">
      <div class="welcome-content">
        <div class="welcome-text">
          <h2>欢迎回来，{{ userStore.userInfo?.username || '用户' }}！</h2>
          <p>今天是 {{ currentDate }}，祝您工作愉快！</p>
          <p>您的岗位：{{ userStore.userInfo?.position || '未设置' }}</p>
        </div>
        <div class="welcome-avatar">
          <el-avatar :size="80">
            <el-icon size="40"><User /></el-icon>
          </el-avatar>
        </div>
      </div>
    </el-card>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <el-card class="stat-card" v-for="stat in stats" :key="stat.title">
        <div class="stat-content">
          <div class="stat-icon" :style="{ backgroundColor: stat.color }">
            <el-icon :size="24">
              <component :is="stat.icon" />
            </el-icon>
          </div>
          <div class="stat-info">
            <h3>{{ stat.value }}</h3>
            <p>{{ stat.title }}</p>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 快捷操作 -->
    <el-card class="quick-actions-card">
      <template #header>
        <span>快捷操作</span>
      </template>
      <div class="quick-actions">
        <el-button
          v-for="action in quickActions"
          :key="action.name"
          :type="action.type"
          :icon="action.icon"
          @click="handleQuickAction(action.action)"
          class="action-btn"
        >
          {{ action.name }}
        </el-button>
      </div>
    </el-card>

    <!-- 最近活动和通知 -->
    <div class="bottom-grid">
      <el-card class="recent-activity-card">
        <template #header>
          <span>最近活动</span>
        </template>
        <el-timeline>
          <el-timeline-item
            v-for="activity in recentActivities"
            :key="activity.id"
            :timestamp="activity.timestamp"
            :type="activity.type"
            size="large"
          >
            <div class="activity-content">
              <h4>{{ activity.title }}</h4>
              <p>{{ activity.description }}</p>
            </div>
          </el-timeline-item>
        </el-timeline>
      </el-card>

      <el-card class="notifications-card">
        <template #header>
          <div class="card-header">
            <span>系统通知</span>
            <el-badge :value="unreadCount" class="notification-badge">
              <el-icon><Bell /></el-icon>
            </el-badge>
          </div>
        </template>
        <div class="notifications-list">
          <div
            v-for="notification in notifications"
            :key="notification.id"
            class="notification-item"
            :class="{ unread: !notification.read }"
            @click="markAsRead(notification.id)"
          >
            <div class="notification-content">
              <h4>{{ notification.title }}</h4>
              <p>{{ notification.content }}</p>
              <span class="notification-time">{{ notification.time }}</span>
            </div>
            <div v-if="!notification.read" class="unread-dot"></div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/counter'
import {
  User,
  DataAnalysis,
  Document,
  Setting,
  Bell,
  Plus,
  Search,
  Download,
  Upload
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const router = useRouter()
const userStore = useUserStore()

// 当前日期
const currentDate = computed(() => {
  return new Date().toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })
})

// 统计数据
const stats = ref([
  {
    title: '今日访问',
    value: '1,234',
    icon: 'DataAnalysis',
    color: '#409EFF'
  },
  {
    title: '总用户数',
    value: '5,678',
    icon: 'User',
    color: '#67C23A'
  },
  {
    title: '文档数量',
    value: '890',
    icon: 'Document',
    color: '#E6A23C'
  },
  {
    title: '系统状态',
    value: '正常',
    icon: 'Setting',
    color: '#F56C6C'
  }
])

// 快捷操作
const quickActions = ref([
  {
    name: '新建文档',
    type: 'primary',
    icon: Plus,
    action: 'create-document'
  },
  {
    name: '数据查询',
    type: 'success',
    icon: Search,
    action: 'search-data'
  },
  {
    name: '导出报表',
    type: 'warning',
    icon: Download,
    action: 'export-report'
  },
  {
    name: '导入数据',
    type: 'info',
    icon: Upload,
    action: 'import-data'
  }
])

// 最近活动
const recentActivities = ref([
  {
    id: 1,
    title: '用户登录',
    description: '您已成功登录系统',
    timestamp: new Date().toLocaleString(),
    type: 'primary'
  },
  {
    id: 2,
    title: '数据更新',
    description: '系统数据已更新',
    timestamp: new Date(Date.now() - 1000 * 60 * 30).toLocaleString(),
    type: 'success'
  },
  {
    id: 3,
    title: '备份完成',
    description: '数据备份已完成',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toLocaleString(),
    type: 'info'
  }
])

// 通知列表
const notifications = ref([
  {
    id: 1,
    title: '系统维护通知',
    content: '系统将于今晚22:00-24:00进行维护',
    time: '2小时前',
    read: false
  },
  {
    id: 2,
    title: '新功能上线',
    content: '个人中心功能已上线，快去体验吧！',
    time: '1天前',
    read: false
  },
  {
    id: 3,
    title: '安全提醒',
    content: '请定期修改您的登录密码',
    time: '3天前',
    read: true
  }
])

// 未读通知数量
const unreadCount = computed(() => {
  return notifications.value.filter(n => !n.read).length
})

// 处理快捷操作
const handleQuickAction = (action: string) => {
  switch (action) {
    case 'create-document':
      ElMessage.info('新建文档功能开发中...')
      break
    case 'search-data':
      ElMessage.info('数据查询功能开发中...')
      break
    case 'export-report':
      ElMessage.info('导出报表功能开发中...')
      break
    case 'import-data':
      ElMessage.info('导入数据功能开发中...')
      break
    default:
      ElMessage.info('功能开发中...')
  }
}

// 标记通知为已读
const markAsRead = (id: number) => {
  const notification = notifications.value.find(n => n.id === id)
  if (notification) {
    notification.read = true
  }
}

// 组件挂载时获取用户信息
onMounted(async () => {
  if (userStore.isLoggedIn && !userStore.userInfo) {
    await userStore.fetchCurrentUser()
  }
})
</script>

<style scoped>
.dashboard-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.welcome-card :deep(.el-card__body) {
  padding: 30px;
}

.welcome-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-text h2 {
  margin: 0 0 10px 0;
  font-size: 24px;
  font-weight: 600;
}

.welcome-text p {
  margin: 5px 0;
  opacity: 0.9;
}

.welcome-avatar {
  flex-shrink: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.stat-card {
  transition: transform 0.3s, box-shadow 0.3s;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-info h3 {
  margin: 0 0 5px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.stat-info p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.quick-actions-card {
  margin: 20px 0;
}

.quick-actions {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.action-btn {
  flex: 1;
  min-width: 120px;
  height: 50px;
}

.bottom-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.recent-activity-card,
.notifications-card {
  height: 400px;
}

.recent-activity-card :deep(.el-card__body),
.notifications-card :deep(.el-card__body) {
  height: calc(100% - 60px);
  overflow-y: auto;
}

.activity-content h4 {
  margin: 0 0 5px 0;
  color: #303133;
}

.activity-content p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.notification-badge {
  cursor: pointer;
}

.notifications-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.notification-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.notification-item:hover {
  background-color: #f5f7fa;
  border-color: #409EFF;
}

.notification-item.unread {
  background-color: #f0f9ff;
  border-color: #409EFF;
}

.notification-content h4 {
  margin: 0 0 5px 0;
  font-size: 14px;
  color: #303133;
}

.notification-content p {
  margin: 0 0 5px 0;
  font-size: 12px;
  color: #606266;
}

.notification-time {
  font-size: 11px;
  color: #909399;
}

.unread-dot {
  width: 8px;
  height: 8px;
  background-color: #409EFF;
  border-radius: 50%;
  flex-shrink: 0;
  margin-top: 5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .welcome-content {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .quick-actions {
    flex-direction: column;
  }
  
  .action-btn {
    min-width: auto;
  }
  
  .bottom-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .dashboard-container {
    padding: 0 10px;
  }
  
  .welcome-card :deep(.el-card__body) {
    padding: 20px;
  }
  
  .welcome-text h2 {
    font-size: 20px;
  }
}
</style>
