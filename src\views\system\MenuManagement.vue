<template>
  <div class="menu-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>菜单管理</span>
        </div>
      </template>

      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :model="searchForm" inline>
          <!-- <el-form-item label="菜单名称">
            <el-input v-model="searchForm.menuName" placeholder="请输入菜单名称" clearable />
          </el-form-item> -->
          <el-form-item>
            <!-- <el-button type="primary" @click="loadMenuTree">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetSearch">重置</el-button> -->
            <el-button type="primary" @click="showAddDialog">
              <el-icon><Plus /></el-icon>
              新增菜单
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 菜单树表格 -->
      <el-table
        ref="tableRef"
        :data="menuTreeData"
        style="width: 100%"
        row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        v-loading="loading"
        :row-class-name="getRowClassName"
        class="draggable-table"
        @row-click="handleRowClick"
      >
        <el-table-column prop="menuName" label="菜单名称" min-width="150" />
        <el-table-column prop="icon" label="图标" min-width="150">
          <template #default="{ row }">
            <el-icon v-if="row.icon">
              <component :is="getIconComponent(row.icon)" />
            </el-icon>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="path" label="路由地址" min-width="150" />
        <el-table-column prop="component" label="组件路径" min-width="150" />
        <el-table-column prop="remark" label="备注" min-width="150" />
        <el-table-column label="操作" width="240" align="center" fixed="right" prop="actions">
          <template #default="{ row }">
            <div class="action-buttons" @click.stop>
              <el-button type="primary" size="small" @click="showEditDialog(row)">编辑</el-button>
              <el-button type="success" size="small" @click="showAddDialog(row)">新增</el-button>
              <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="600px" @close="resetForm">
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px">
        <el-form-item label="父菜单" prop="parentId">
          <el-tree-select
            v-model="formData.parentId"
            :data="menuTreeOptions"
            :props="{ label: 'menuName', value: 'id', children: 'children' }"
            placeholder="请选择父菜单"
            check-strictly
            clearable
          />
        </el-form-item>
        <el-form-item label="菜单名称" prop="menuName">
          <el-input v-model="formData.menuName" placeholder="请输入菜单名称" />
        </el-form-item>
        <el-form-item label="菜单图标" prop="icon">
          <el-input v-model="formData.icon" placeholder="请输入图标名称">
            <template #prepend>
              <el-icon v-if="formData.icon">
                <component :is="getIconComponent(formData.icon)" />
              </el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="路由地址" prop="path">
          <el-input v-model="formData.path" placeholder="请输入路由地址" />
        </el-form-item>
        <el-form-item label="组件路径" prop="component">
          <el-input v-model="formData.component" placeholder="请输入组件路径" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="formData.remark" placeholder="请输入备注" type="textarea" :rows="2" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting"> 确定 </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onBeforeUnmount, nextTick, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRoute } from 'vue-router'
import { usePageStatePersist } from '@/stores/pageState'
import {
  Plus,
  Setting,
  User,
  Document,
  Management,
  UserFilled,
  Menu as MenuIcon,
  Grid,
  DataAnalysis,
  Monitor,
  Tools,
  Files,
  List,
  Search,
  Flag,
  House,
} from '@element-plus/icons-vue'
import {
  getMenuTree,
  addMenu,
  updateMenu,
  deleteMenu,
  batchUpdateMenuSort,
  type SysMenu,
  type MenuAddDTO,
  type MenuUpdateDTO,
} from '@/api/system/user'
import { initTreeTableDrag, destroyTreeTableDrag } from '@/utils/treeTableDrag'
import { handleTreeTableSort } from '@/utils/treeTableSort'
import type { FormInstance } from 'element-plus'
import type Sortable from 'sortablejs'

// 页面状态管理
const route = useRoute()
const { saveCurrentState, restoreState } = usePageStatePersist(route.path)

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const dialogVisible = ref(false)
const menuTreeData = ref<SysMenu[]>([])
const menuTreeOptions = ref<SysMenu[]>([])
const sortableInstance = ref<Sortable | null>(null)
const tableRef = ref()

// 搜索表单
const searchForm = reactive({
  menuName: '',
})

// 保存页面状态
const savePageState = () => {
  const state = {
    searchForm: { ...searchForm },
    menuTreeData: [...menuTreeData.value],
    menuTreeOptions: [...menuTreeOptions.value],
    loading: loading.value,
  }
  saveCurrentState(state)
}

// 恢复页面状态
const restorePageState = () => {
  const savedState = restoreState()
  if (savedState) {
    Object.assign(searchForm, savedState.searchForm)
    menuTreeData.value = savedState.menuTreeData || []
    menuTreeOptions.value = savedState.menuTreeOptions || []
    // 恢复状态时确保loading为false
    loading.value = false
    return true
  }
  return false
}

// 表单数据
const formData = reactive<MenuAddDTO & { id?: number }>({
  parentId: 0,
  menuName: '',
  icon: '',
  path: '',
  component: '',
  remark: '',
  sortOrder: 0, // 保持接口兼容性，但不在界面上显示
})

// 表单引用
const formRef = ref<FormInstance>()

// 表单验证规则
const formRules = {
  menuName: [{ required: true, message: '请输入菜单名称', trigger: 'blur' }],
}

// 对话框标题
const dialogTitle = ref('新增菜单')

// 图标组件已在顶部导入

// 图标组件映射
const iconComponents: Record<string, any> = {
  Setting,
  User,
  Document,
  Management,
  UserFilled,
  Menu: MenuIcon,
  Grid,
  DataAnalysis,
  Monitor,
  Tools,
  Files,
  List,
  Search,
  Flag,
  House,
}

// 获取图标组件
const getIconComponent = (iconName: string) => {
  return iconComponents[iconName] || Setting
}

/**
 * 获取表格行类名
 * 为可拖拽的行添加特定样式，为有子菜单的行添加可点击样式
 * @param params 行参数
 * @returns 行类名
 */
const getRowClassName = (params: { row: SysMenu; rowIndex: number }) => {
  const { row } = params
  let className = 'draggable-row'

  // 如果有子菜单，添加可点击样式
  if (row.children && row.children.length > 0) {
    className += ' has-children'
  }

  return className
}

/**
 * 安全销毁当前拖拽实例
 * 彻底清理所有拖拽相关资源，确保销毁完成后再进行后续操作
 * @returns Promise 销毁完成的Promise
 */
const safeDestroyDrag = async (): Promise<void> => {
  if (sortableInstance.value) {
    try {
      // 检查是否正在拖拽中，如果是则等待拖拽完成
      if ((sortableInstance.value as any)._dragStarted) {
        console.log('Sortable实例正在拖拽中，等待拖拽完成')
        // 等待一小段时间让拖拽完成
        await new Promise((resolve) => setTimeout(resolve, 200))
      }

      // 先移除全局事件监听器
      try {
        if (
          sortableInstance.value &&
          typeof (sortableInstance.value as any)._onDragOver === 'function'
        ) {
          document.removeEventListener('dragover', (sortableInstance.value as any)._onDragOver)
        }
        if (
          sortableInstance.value &&
          typeof (sortableInstance.value as any)._onDrop === 'function'
        ) {
          document.removeEventListener('drop', (sortableInstance.value as any)._onDrop)
        }
        if (
          sortableInstance.value &&
          typeof (sortableInstance.value as any)._onDragEnd === 'function'
        ) {
          document.removeEventListener('dragend', (sortableInstance.value as any)._onDragEnd)
        }
      } catch (eventRemoveError) {
        console.warn('移除全局事件监听器失败:', eventRemoveError)
      }

      // 销毁Sortable实例
      destroyTreeTableDrag(sortableInstance.value)

      // 等待更长时间确保销毁完成
      await new Promise((resolve) => setTimeout(resolve, 100))

      // 强制清理所有可能的引用
      sortableInstance.value = null

      // 清理DOM中可能残留的SortableJS相关类和属性
      const tbody = document.querySelector('.el-table__body-wrapper tbody') as HTMLElement | null
      if (tbody) {
        // 移除SortableJS添加的类
        tbody.classList.remove('sortable-ghost', 'sortable-chosen', 'sortable-drag')

        // 移除所有tr上的SortableJS相关类
        const rows = tbody.querySelectorAll('tr')
        rows.forEach((row) => {
          row.classList.remove('sortable-ghost', 'sortable-chosen', 'sortable-drag')
        })
      }
    } catch (error) {
      console.warn('销毁拖拽实例失败:', error)
      sortableInstance.value = null
    }
  }
}

/**
 * 初始化拖拽功能
 * 参考用户管理实现，使用插入式排序逻辑优化拖拽体验
 */
const initDrag = async () => {
  await nextTick() // 确保DOM完全渲染

  if (!menuTreeData.value || menuTreeData.value.length === 0) {
    console.log('菜单树数据为空，跳过拖拽初始化')
    return
  }

  try {
    // 先销毁现有实例，确保完全清理
    await safeDestroyDrag()

    // 等待更长时间确保DOM更新和清理完成
    await new Promise((resolve) => setTimeout(resolve, 200))

    // 获取表格tbody元素
    const tbody = document.querySelector('.el-table__body-wrapper tbody') as HTMLElement | null
    if (!tbody) {
      console.error('未找到表格tbody')
      return
    }

    // 检查tbody是否可见且可交互
    const tbodyRect = tbody.getBoundingClientRect()
    if (tbodyRect.width === 0 || tbodyRect.height === 0) {
      console.warn('表格tbody不可见，跳过拖拽初始化')
      return
    }

    // 扁平化菜单数据用于拖拽 - 按照DOM显示顺序
    const flattenMenuItems = (items: SysMenu[]): SysMenu[] => {
      const result: SysMenu[] = []
      // 按照sortOrder排序后再扁平化
      const sortedItems = [...items].sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0))
      sortedItems.forEach((item) => {
        result.push(item)
        if (item.children && item.children.length > 0) {
          // 子菜单也按sortOrder排序
          const sortedChildren = [...item.children].sort(
            (a, b) => (a.sortOrder || 0) - (b.sortOrder || 0),
          )
          result.push(...flattenMenuItems(sortedChildren))
        }
      })
      return result
    }

    const flatMenuData = flattenMenuItems(menuTreeData.value)

    // 为每行设置 data-row-key 属性，确保与扁平化数据顺序一致
    const rows = tbody.querySelectorAll('tr')

    rows.forEach((row, index) => {
      if (index < flatMenuData.length) {
        const menuItem = flatMenuData[index]
        row.setAttribute('data-row-key', menuItem.id.toString())
        // 添加调试信息
        row.setAttribute('data-menu-name', menuItem.menuName)
        row.setAttribute('data-parent-id', menuItem.parentId.toString())
        row.setAttribute('data-sort-order', menuItem.sortOrder.toString())
      } else {
        console.warn(`⚠️ 行${index}超出扁平化数据范围，使用备用ID`)
        row.setAttribute('data-row-key', index.toString())
      }
    })

    // 再次检查是否已经有拖拽实例，避免重复初始化
    if (sortableInstance.value) {
      console.warn('发现未销毁的拖拽实例，强制清理')
      await safeDestroyDrag()
      await new Promise((resolve) => setTimeout(resolve, 100))
    }

    // 初始化树形表格拖拽
    sortableInstance.value = initTreeTableDrag(tbody, handleDragEnd, {
      animation: 150,
      ghostClass: 'sortable-ghost',
      chosenClass: 'sortable-chosen',
      dragClass: 'sortable-drag',
      handle: '.draggable-row',
      treeData: menuTreeData.value, // 传递树形数据
    })

    console.log('树形表格拖拽初始化成功')
  } catch (error) {
    console.error('初始化拖拽功能失败:', error)
    ElMessage.error('拖拽功能初始化失败')
  }
}

/**
 * 处理拖拽结束事件 - 实现插入式排序
 * 参考用户管理实现，优化排序逻辑和错误处理
 * @param oldIndex 原始位置索引
 * @param newIndex 新位置索引
 */
const handleDragEnd = async (oldIndex: number, newIndex: number) => {
  if (oldIndex === newIndex) {
    return
  }

  try {
    // 获取表格tbody元素
    const tbody = document.querySelector('.el-table__body-wrapper tbody') as HTMLElement | null
    if (!tbody) {
      console.warn('未找到表格tbody')
      return
    }

    // 获取所有可见的行
    const tbodyChildren = Array.from(tbody.children)
    if (newIndex >= tbodyChildren.length || oldIndex >= tbodyChildren.length) {
      console.warn('索引超出范围:', { oldIndex, newIndex, totalChildren: tbodyChildren.length })
      return
    }

    const oldRow = tbodyChildren[oldIndex] as HTMLElement
    const newRow = tbodyChildren[newIndex] as HTMLElement

    // 获取行的数据ID - 简化获取逻辑
    const getRowId = (row: HTMLElement | null): string | null => {
      if (!row) return null
      let rowId = row.getAttribute('data-row-key')
      if (rowId) return rowId

      const possibleAttrs = ['row-key', 'data-id', 'id']
      for (const attr of possibleAttrs) {
        rowId = row.getAttribute(attr)
        if (rowId) return rowId
      }
      return null
    }

    const oldRowId = getRowId(oldRow)
    const newRowId = getRowId(newRow)

    if (!oldRowId || !newRowId) {
      console.warn('无法获取行数据ID', { oldRowId, newRowId })
      return
    }

    // 在树形数据中查找对应的菜单项 - 改进查找逻辑
    const findMenuItem = (items: SysMenu[], id: string): SysMenu | null => {
      for (const item of items) {
        if (item.id.toString() === id) {
          return item
        }
        if (item.children && item.children.length > 0) {
          const found = findMenuItem(item.children, id)
          if (found) return found
        }
      }
      return null
    }

    // 先尝试通过扁平化数据查找，确保与DOM顺序一致
    const findMenuItemByFlatIndex = (flatIndex: number): SysMenu | null => {
      const flattenMenuItems = (items: SysMenu[]): SysMenu[] => {
        const result: SysMenu[] = []
        const sortedItems = [...items].sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0))
        sortedItems.forEach((item) => {
          result.push(item)
          if (item.children && item.children.length > 0) {
            const sortedChildren = [...item.children].sort(
              (a, b) => (a.sortOrder || 0) - (b.sortOrder || 0),
            )
            result.push(...flattenMenuItems(sortedChildren))
          }
        })
        return result
      }

      const flatData = flattenMenuItems(menuTreeData.value)
      if (flatIndex >= 0 && flatIndex < flatData.length) {
        return flatData[flatIndex]
      }
      return null
    }

    // 优先使用扁平化数据查找，确保与DOM顺序一致
    let draggedItem = findMenuItemByFlatIndex(oldIndex)
    let targetItem = findMenuItemByFlatIndex(newIndex)

    // 如果扁平化查找失败，回退到树形查找
    if (!draggedItem) {
      draggedItem = findMenuItem(menuTreeData.value, oldRowId)
      console.warn('通过扁平化索引未找到拖拽项，使用ID查找:', oldRowId)
    }

    if (!targetItem) {
      targetItem = findMenuItem(menuTreeData.value, newRowId)
      console.warn(' 通过扁平化索引未找到目标项，使用ID查找:', newRowId)
    }

    // 检查是否为同级拖拽
    if (draggedItem && targetItem && draggedItem.parentId !== targetItem.parentId) {
      console.warn('跨级拖拽检测，当前实现仅支持同级拖拽', {
        draggedParent: draggedItem.parentId,
        targetParent: targetItem.parentId,
      })
      ElMessage.warning('当前仅支持同级菜单之间的拖拽排序')
      return
    }

    if (!draggedItem || !targetItem) {
      console.warn('未找到对应的菜单项', { draggedItem, targetItem })
      return
    }

    // 使用树形排序工具处理排序 - 参考用户管理的插入式排序
    const sortResult = handleTreeTableSort({
      treeData: menuTreeData.value,
      draggedItem,
      targetItem,
      oldIndex,
      newIndex,
    })

    console.log('排序结果:', {
      success: sortResult.success,
      message: sortResult.message,
      itemsToUpdate: sortResult.itemsToUpdate?.length || 0,
    })

    if (!sortResult.success) {
      console.warn('排序失败:', sortResult.message)
      ElMessage.warning(sortResult.message)
      return
    }

    // 批量更新排序值 - 使用批量接口优化性能
    try {
      // 提取只需要更新的排序数据，减少数据传输量
      const sortUpdateData = sortResult.itemsToUpdate.map((item) => ({
        id: item.id,
        sortOrder: item.sortOrder,
      }))

      console.log('批量更新排序数据:', sortUpdateData)

      // 使用批量更新接口，一次请求完成所有排序更新
      const response = (await batchUpdateMenuSort(sortUpdateData)) as any

      if (response.code === 1) {
        console.log('拖拽排序完成并保存到后端')
        ElMessage.success('排序更新成功')

        // 重新加载菜单树数据
        await loadMenuTree()
        console.log('菜单树数据已重新加载')
      } else {
        console.error('批量更新排序失败:')
        ElMessage.error('更新排序失败')

        // 失败时重新加载数据恢复状态
        await loadMenuTree()
      }
    } catch (error) {
      console.error('批量更新排序失败:', error)
      ElMessage.error('更新排序失败')

      // 失败时重新加载数据恢复状态
      await loadMenuTree()
    }
  } catch (error) {
    console.error('拖拽处理失败:', error)
    ElMessage.error('拖拽处理失败')

    // 失败时重新加载数据恢复状态
    await loadMenuTree()
    console.log('失败后已重新加载数据恢复状态')
  }
}

/**
 * 加载菜单树
 * 参考用户管理实现，优化数据加载和拖拽初始化逻辑
 */
const loadMenuTree = async () => {
  loading.value = true
  try {
    const response = await getMenuTree()
    if (response.code === 1) {
      menuTreeData.value = response.data
      // 构建树形选择器选项（添加根节点）
      menuTreeOptions.value = [
        {
          id: 0,
          parentId: -1,
          menuName: '根菜单',
          icon: '',
          path: '',
          component: '',
          remark: '',
          sortOrder: 0,
          children: response.data,
        },
      ]
      // 保存页面状态
      savePageState()

      // 不在这里重新初始化拖拽，由watch监听器自动处理
      // 避免重复初始化和潜在的冲突
    } else {
      ElMessage.error(response.msg || '获取菜单树失败')
    }
  } catch (error: any) {
    ElMessage.error(error.message || '获取菜单树失败')
  } finally {
    loading.value = false
  }
}

// 重置搜索
const resetSearch = () => {
  searchForm.menuName = ''
  loadMenuTree()
}

// 显示新增对话框
const showAddDialog = (parentMenu?: SysMenu) => {
  dialogTitle.value = '新增菜单'
  formData.parentId = parentMenu?.id || 0
  formData.menuName = ''
  formData.icon = ''
  formData.path = ''
  formData.component = ''
  formData.remark = ''
  formData.sortOrder = 0 // 设置为默认值
  delete formData.id
  dialogVisible.value = true
}

// 显示编辑对话框
const showEditDialog = (menu: SysMenu) => {
  dialogTitle.value = '编辑菜单'
  formData.id = menu.id
  formData.parentId = menu.parentId
  formData.menuName = menu.menuName
  formData.icon = menu.icon
  formData.path = menu.path
  formData.component = menu.component
  formData.remark = menu.remark || ''
  formData.sortOrder = menu.sortOrder // 保持原有排序
  dialogVisible.value = true
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    submitting.value = true

    if (formData.id) {
      // 编辑
      const updateData: MenuUpdateDTO = {
        id: formData.id,
        parentId: formData.parentId,
        menuName: formData.menuName,
        icon: formData.icon,
        path: formData.path,
        component: formData.component,
        remark: formData.remark,
        sortOrder: formData.sortOrder, // 保持原有排序值
      }
      const response = await updateMenu(updateData)
      if (response.code === 1) {
        ElMessage.success('更新成功')
        dialogVisible.value = false
        loadMenuTree()
      } else {
        ElMessage.error(response.msg || '更新失败')
      }
    } else {
      // 新增时不包含 sortOrder，让后端自动处理排序
      const addData: MenuAddDTO = {
        parentId: formData.parentId,
        menuName: formData.menuName,
        icon: formData.icon,
        path: formData.path,
        component: formData.component,
        remark: formData.remark,
        sortOrder: 0, // 设置为默认值，后端会自动处理
      }
      const response = await addMenu(addData)
      if (response.code === 1) {
        ElMessage.success('新增成功')
        dialogVisible.value = false
        loadMenuTree()
      } else {
        ElMessage.error(response.msg || '新增失败')
      }
    }
  } catch (error: any) {
    ElMessage.error(error.message || '操作失败')
  } finally {
    submitting.value = false
  }
}

// 删除菜单
const handleDelete = async (menu: SysMenu) => {
  try {
    await ElMessageBox.confirm(`确定要删除菜单"${menu.menuName}"吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    const response = await deleteMenu(menu.id)
    if (response.code === 1) {
      ElMessage.success('删除成功')
      loadMenuTree()
    } else {
      ElMessage.error(response.msg || '删除失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '删除失败')
    }
  }
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
}

/**
 * 处理表格行点击事件
 * 点击行时切换展开/收起状态
 * @param row 被点击的行数据
 * @param column 被点击的列
 * @param event 点击事件
 */
const handleRowClick = (row: SysMenu, column: any, event: Event) => {
  // 如果点击的是操作列，则不处理展开逻辑
  if (column && (column.property === 'actions' || column.label === '操作')) {
    return
  }

  // 检查是否点击了按钮元素（额外的安全检查）
  const target = event.target as HTMLElement
  if (target && (target.tagName === 'BUTTON' || target.closest('button'))) {
    return
  }

  // 如果该行有子菜单，则切换展开状态
  if (row.children && row.children.length > 0) {
    // 获取表格实例
    const table = tableRef.value
    if (table) {
      try {
        // 切换行的展开状态
        table.toggleRowExpansion(row)
      } catch (error) {
        console.warn('切换行展开状态失败:', error)
      }
    }
  }
}

/**
 * 组件挂载时加载数据并初始化拖拽功能
 * 参考用户管理实现，优化初始化逻辑
 */
onMounted(async () => {
  // 尝试恢复页面状态
  const hasRestoredState = restorePageState()

  // 如果没有保存的状态或者菜单树为空，则重新加载数据
  if (!hasRestoredState || menuTreeData.value.length === 0) {
    await loadMenuTree()
  }

  // 初始化拖拽功能
  await initDrag()
})

/**
 * 监听菜单树数据变化，重新初始化拖拽功能
 * 参考用户管理实现，使用优化的插入式排序逻辑
 */
watch(
  () => menuTreeData.value,
  async (newData) => {
    if (newData.length > 0 && tableRef.value) {
      await nextTick()
      await nextTick()
      try {
        // 销毁旧的拖拽实例
        if (sortableInstance.value) {
          destroyTreeTableDrag(sortableInstance.value)
          sortableInstance.value = null
        }

        // 重新初始化拖拽功能
        await initDrag()
        console.log('拖拽功能重新初始化成功')
      } catch (error) {
        console.error('重新初始化拖拽功能失败:', error)
      }
    }
  },
  { deep: true },
)

/**
 * 在组件卸载前保存状态并安全销毁拖拽实例
 * 参考用户管理实现，避免内存泄漏和潜在的错误
 */
onBeforeUnmount(async () => {
  try {
    savePageState()
    // 安全销毁拖拽实例
    await safeDestroyDrag()
  } catch (error) {
    console.error('组件卸载时销毁拖拽实例失败:', error)
  }
})
</script>

<style scoped>
.menu-management {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-area {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  align-items: center;
  white-space: nowrap;
}

.action-buttons .el-button {
  margin: 0;
}

/* 拖拽相关样式 - 参考用户管理实现 */
.sortable-ghost {
  opacity: 0.5;
  background-color: #e5e7eb;
}

.el-table__body-wrapper tbody tr {
  cursor: grab;
}

.el-table__body-wrapper tbody tr.sortable-ghost {
  cursor: grabbing;
}

.draggable-table {
  /* 整行拖拽样式 */
  .draggable-row {
    cursor: move;
    transition: all 0.3s ease;

    &:hover {
      background-color: rgba(64, 158, 255, 0.05) !important;
    }
  }

  /* 有子菜单的行可以点击展开 */
  :deep(.el-table__row) {
    &.has-children {
      cursor: pointer;

      &:hover {
        background-color: rgba(64, 158, 255, 0.08) !important;
      }

      &:hover .el-table__expand-icon {
        color: #409eff;
        transform: scale(1.1);
      }
    }
  }

  .sortable-ghost {
    opacity: 0.4;
    background: #f5f7fa !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .sortable-chosen {
    background: #e6f7ff !important;
    box-shadow: 0 2px 12px rgba(64, 158, 255, 0.2);
  }

  .sortable-drag {
    opacity: 0.8;
    background: #e6f7ff !important;
    box-shadow: 0 4px 16px rgba(64, 158, 255, 0.3);
    transform: rotate(2deg);
  }

  .menu-name-cell {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

/* 整行拖拽样式 */
:deep(.el-table__row) {
  cursor: move;
  transition: all 0.3s ease;
}
</style>
