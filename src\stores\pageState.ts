import { defineStore } from 'pinia'
import { ref } from 'vue'

// 页面状态接口
interface PageState {
  [key: string]: any
}

// 用户管理页面状态
interface UserManagementState {
  searchForm: {
    username: string
    position: string
    status: string
  }
  pagination: {
    pageNum: number
    pageSize: number
    total: number
  }
  userList: any[]
  selectedUsers: any[]
  loading: boolean
}

// 菜单管理页面状态
interface MenuManagementState {
  searchForm: {
    menuName: string
    status: string
  }
  menuList: any[]
  expandedKeys: string[]
  loading: boolean
}

export const usePageStateStore = defineStore('pageState', () => {
  // 存储所有页面的状态
  const pageStates = ref<Record<string, PageState>>({})

  // 保存页面状态
  const savePageState = (routePath: string, state: PageState) => {
    pageStates.value[routePath] = { ...state }
  }

  // 获取页面状态
  const getPageState = (routePath: string): PageState | null => {
    return pageStates.value[routePath] || null
  }

  // 清除页面状态
  const clearPageState = (routePath: string) => {
    delete pageStates.value[routePath]
  }

  // 清除所有页面状态
  const clearAllPageStates = () => {
    pageStates.value = {}
  }

  // 用户管理页面状态管理
  const saveUserManagementState = (state: UserManagementState) => {
    savePageState('/system/users', state)
  }

  const getUserManagementState = (): UserManagementState | null => {
    return getPageState('/system/users') as UserManagementState | null
  }

  // 菜单管理页面状态管理
  const saveMenuManagementState = (state: MenuManagementState) => {
    savePageState('/system/menu', state)
  }

  const getMenuManagementState = (): MenuManagementState | null => {
    return getPageState('/system/menu') as MenuManagementState | null
  }

  return {
    pageStates,
    savePageState,
    getPageState,
    clearPageState,
    clearAllPageStates,
    saveUserManagementState,
    getUserManagementState,
    saveMenuManagementState,
    getMenuManagementState,
  }
})

// 页面状态保持的组合式函数
export const usePageStatePersist = (routePath: string) => {
  const pageStateStore = usePageStateStore()

  // 保存当前页面状态
  const saveCurrentState = (state: PageState) => {
    pageStateStore.savePageState(routePath, state)
  }

  // 恢复页面状态
  const restoreState = (): PageState | null => {
    return pageStateStore.getPageState(routePath)
  }

  // 清除当前页面状态
  const clearCurrentState = () => {
    pageStateStore.clearPageState(routePath)
  }

  return {
    saveCurrentState,
    restoreState,
    clearCurrentState,
  }
}
