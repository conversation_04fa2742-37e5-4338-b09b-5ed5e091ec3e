/**
 * 表格排序工具函数
 */

// 排序数据接口
export interface SortData {
  sortField: string
  sortOrder: string
}

// 排序变化事件参数
export interface SortChangeParams {
  prop: string
  order: string | null
}

/**
 * 处理表格排序变化
 * @param sortData - 排序数据对象
 * @param params - 排序变化参数
 * @param callback - 排序后的回调函数
 */
export const handleTableSort = (
  sortData: SortData,
  params: SortChangeParams,
  callback?: () => void
) => {
  const { prop, order } = params
  
  if (order) {
    sortData.sortField = prop
    sortData.sortOrder = order === 'ascending' ? 'asc' : 'desc'
  } else {
    sortData.sortField = ''
    sortData.sortOrder = ''
  }
  
  // 执行回调函数（通常是重新加载数据）
  if (callback) {
    callback()
  }
}

/**
 * 重置排序状态
 * @param sortData - 排序数据对象
 */
export const resetSort = (sortData: SortData) => {
  sortData.sortField = ''
  sortData.sortOrder = ''
}

/**
 * 获取排序参数（用于API请求）
 * @param sortData - 排序数据对象
 * @returns 排序参数对象
 */
export const getSortParams = (sortData: SortData) => {
  return {
    sortField: sortData.sortField,
    sortOrder: sortData.sortOrder,
  }
}

/**
 * 创建排序数据的响应式对象
 * @param defaultField - 默认排序字段
 * @param defaultOrder - 默认排序方向
 * @returns 响应式排序数据对象
 */
export const createSortData = (defaultField = '', defaultOrder = '') => {
  return {
    sortField: defaultField,
    sortOrder: defaultOrder,
  }
}

/**
 * 表格列配置生成器
 * @param columns - 列配置数组
 * @returns 带排序功能的列配置
 */
export interface ColumnConfig {
  prop: string
  label: string
  width?: string | number
  minWidth?: string | number
  align?: 'left' | 'center' | 'right'
  sortable?: boolean
  fixed?: boolean | 'left' | 'right'
}

export const generateSortableColumns = (columns: ColumnConfig[]) => {
  return columns.map(column => ({
    ...column,
    sortable: column.sortable ? 'custom' : false,
  }))
}

/**
 * 本地排序函数（用于前端排序）
 * @param data - 要排序的数据数组
 * @param sortField - 排序字段
 * @param sortOrder - 排序方向
 * @returns 排序后的数据
 */
export const localSort = <T extends Record<string, any>>(
  data: T[],
  sortField: string,
  sortOrder: string
): T[] => {
  if (!sortField || !sortOrder) {
    return data
  }

  return [...data].sort((a, b) => {
    const aValue = a[sortField]
    const bValue = b[sortField]
    
    // 处理null/undefined值
    if (aValue == null && bValue == null) return 0
    if (aValue == null) return sortOrder === 'asc' ? -1 : 1
    if (bValue == null) return sortOrder === 'asc' ? 1 : -1
    
    // 数字比较
    if (typeof aValue === 'number' && typeof bValue === 'number') {
      return sortOrder === 'asc' ? aValue - bValue : bValue - aValue
    }
    
    // 字符串比较
    const aStr = String(aValue).toLowerCase()
    const bStr = String(bValue).toLowerCase()
    
    if (aStr < bStr) return sortOrder === 'asc' ? -1 : 1
    if (aStr > bStr) return sortOrder === 'asc' ? 1 : -1
    return 0
  })
}

/**
 * 排序状态管理的组合式函数
 * @param loadDataCallback - 加载数据的回调函数
 * @returns 排序相关的方法和数据
 */
export const useSorting = (loadDataCallback?: () => void) => {
  const sortData = createSortData()
  
  const handleSortChange = (params: SortChangeParams) => {
    handleTableSort(sortData, params, loadDataCallback)
  }
  
  const resetSorting = () => {
    resetSort(sortData)
    if (loadDataCallback) {
      loadDataCallback()
    }
  }
  
  const getSortingParams = () => {
    return getSortParams(sortData)
  }
  
  return {
    sortData,
    handleSortChange,
    resetSorting,
    getSortingParams,
  }
}

/**
 * 排序图标组件配置
 */
export const sortIcons = {
  ascending: 'ArrowUp',
  descending: 'ArrowDown',
  default: 'Sort',
}

/**
 * 获取排序状态的显示文本
 * @param sortField - 当前排序字段
 * @param sortOrder - 当前排序方向
 * @param fieldName - 字段名称
 * @returns 排序状态文本
 */
export const getSortStatusText = (
  sortField: string,
  sortOrder: string,
  fieldName: string
): string => {
  if (sortField === fieldName) {
    return sortOrder === 'asc' ? '升序' : '降序'
  }
  return '未排序'
}

/**
 * 表格排序的CSS类名生成器
 * @param sortField - 当前排序字段
 * @param sortOrder - 当前排序方向
 * @param fieldName - 字段名称
 * @returns CSS类名
 */
export const getSortClassName = (
  sortField: string,
  sortOrder: string,
  fieldName: string
): string => {
  if (sortField === fieldName) {
    return `sorted-${sortOrder}`
  }
  return 'unsorted'
}
