<template>
  <div class="product-management">
    <!-- 两列布局 -->
    <div class="two-column-layout">
      <!-- 左侧：商品分类 -->
      <div class="left-panel" :style="{ width: leftPanelWidth + 'px' }">
        <div class="panel-header">
          <h4>商品分类</h4>
        </div>
        <div class="category-tree">
          <el-tree
            ref="categoryTreeRef"
            :data="categoryTreeData"
            :props="{ label: 'name', children: 'children' }"
            node-key="id"
            :default-expand-all="true"
            :highlight-current="true"
            :draggable="true"
            :allow-drag="allowDrag"
            :allow-drop="allowDrop"
            @node-click="handleCategoryClick"
            @node-drag-end="handleNodeDragEnd"
          >
            <template #default="{ node, data }">
              <div class="tree-node">
                <span class="node-label">{{ data.name }}</span>
                <span class="node-actions">
                  <el-button type="text" size="small" @click.stop="handleAddSubCategory(data)">
                    <el-icon><Plus /></el-icon>
                    新增
                  </el-button>
                  <el-button
                    v-if="data.parentId !== 0"
                    type="text"
                    size="small"
                    @click.stop="handleEditCategory(data)"
                  >
                    编辑
                  </el-button>
                  <el-button
                    v-if="data.parentId !== 0"
                    type="text"
                    size="small"
                    @click.stop="handleDeleteCategory(data)"
                  >
                    删除
                  </el-button>
                </span>
              </div>
            </template>
          </el-tree>
        </div>
      </div>

      <!-- 分割线 -->
      <div
        class="splitter"
        @mousedown="startLeftResize"
        :class="{ 'splitter-active': isLeftResizing }"
      >
        <div class="splitter-line"></div>
      </div>

      <!-- 右侧：商品管理 -->
      <div class="right-panel" :style="{ flex: 1 }">
        <div class="panel-header">
          <div class="header-left">
            <h4>商品管理</h4>
            <span class="category-info">{{ getCategoryPath(selectedCategory?.id) }}</span>

            <!-- 查询表单 -->
            <el-form :inline="true" :model="searchForm" size="small" class="search-form-inline">
              <el-form-item label="商品名称">
                <el-input
                  v-model="searchForm.name"
                  placeholder="请输入商品名称"
                  clearable
                  style="width: 200px"
                />
              </el-form-item>
              <el-form-item label="商品ID">
                <el-input
                  v-model="searchForm.id"
                  placeholder="请输入商品ID"
                  clearable
                  style="width: 150px"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleSearch"> 查询 </el-button>
                <el-button @click="resetSearch"> 重置 </el-button>
              </el-form-item>
            </el-form>
          </div>
          <div class="header-right">
            <el-button type="success" size="small" @click="handleExportProduct">
              <el-icon><Download /></el-icon>
              导出商品
            </el-button>
            <el-button type="primary" size="small" @click="handleAddProduct">
              <el-icon><plus /></el-icon>
              添加商品
            </el-button>
          </div>
        </div>

        <!-- 商品表格 -->
        <div class="table-container">
          <el-table
            :data="productList"
            border
            v-loading="productLoading"
            class="product-table"
            row-key="id"
            @expand-change="handleExpandChange"
            @row-click="handleRowClick"
            :row-class-name="getRowClassName"
          >
            <el-table-column type="expand" width="50">
              <template #default="{ row }">
                <div class="spec-expand-container">
                  <div class="spec-expand-header">
                    <h4>{{ row.name }} - 规格详情</h4>
                    <el-button type="primary" size="small" @click="handleAddProductSpec(row.id)">
                      <el-icon><Plus /></el-icon>
                      添加规格
                    </el-button>
                  </div>
                  <el-table
                    :data="productSpecsMap.get(row.id) || []"
                    border
                    stripe
                    v-loading="productSpecsLoadingMap.has(row.id)"
                    class="spec-expand-table"
                    :scrollbar-always-on="true"
                  >
                    <el-table-column
                      type="index"
                      label="序号"
                      width="60"
                      align="center"
                      :index="(index) => index + 1"
                    />

                    <el-table-column label="规格名称" width="150">
                      <template #default="{ row: specRow, $index }">
                        <div v-if="specRow.isEditing" class="spec-edit-container">
                          <el-input
                            v-model="specRow.name"
                            size="small"
                            @keydown.enter="handleSaveProductSpec(row.id, specRow, $index)"
                            placeholder="请输入规格名称"
                            style="flex: 1"
                          />
                        </div>
                        <div
                          v-else
                          class="spec-name-container"
                          @dblclick="handleEditProductSpec(row.id, specRow, $index)"
                        >
                          <span class="spec-name">{{ specRow.name }}</span>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="id" label="商品规格ID" width="100" />
                    <el-table-column label="单位" width="80">
                      <template #default="{ row: specRow }">
                        <el-input
                          v-if="specRow.isEditing"
                          v-model="specRow.unit"
                          size="small"
                          placeholder="单位"
                        />
                        <span v-else>{{ specRow.unit }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="换算数量" width="130" align="center">
                      <template #default="{ row: specRow }">
                        <el-input-number
                          v-if="specRow.isEditing"
                          v-model="specRow.conversionNum"
                          size="small"
                          :min="0"
                          :precision="0"
                          style="width: 110px"
                        />
                        <span v-else>{{ specRow.conversionNum }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="价格" width="130" align="center">
                      <template #default="{ row: specRow }">
                        <el-input-number
                          v-if="specRow.isEditing"
                          v-model="specRow.price"
                          size="small"
                          :min="0"
                          :precision="2"
                          style="width: 110px"
                        />
                        <span v-else>{{ specRow.price }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="重量（成品）" width="130" align="center">
                      <template #default="{ row: specRow }">
                        <el-input-number
                          v-if="specRow.isEditing"
                          v-model="specRow.weight"
                          size="small"
                          :min="0"
                          :precision="2"
                          style="width: 110px"
                        />
                        <span v-else>{{ specRow.weight }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="条形码（成品）" width="130">
                      <template #default="{ row: specRow }">
                        <el-input
                          v-if="specRow.isEditing"
                          v-model="specRow.barcode"
                          size="small"
                          placeholder="条形码（成品）"
                        />
                        <span v-else>{{ specRow.barcode }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="体积（成品）" width="130" align="center">
                      <template #default="{ row: specRow }">
                        <el-input-number
                          v-if="specRow.isEditing"
                          v-model="specRow.volume"
                          size="small"
                          :min="0"
                          :precision="2"
                          style="width: 110px"
                        />
                        <span v-else>{{ specRow.volume }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="每箱数量（原料包材）" width="130" align="center">
                      <template #default="{ row: specRow }">
                        <el-input-number
                          v-if="specRow.isEditing"
                          v-model="specRow.perBoxNum"
                          size="small"
                          :min="0"
                          :precision="0"
                          style="width: 110px"
                        />
                        <span v-else>{{ specRow.perBoxNum }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="警戒库存（采购/生产）" width="130" align="center">
                      <template #default="{ row: specRow }">
                        <el-input-number
                          v-if="specRow.isEditing"
                          v-model="specRow.alertInventory"
                          size="small"
                          :min="0"
                          :precision="0"
                          style="width: 110px"
                        />
                        <span v-else>{{ specRow.alertInventory }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="批量数量（采购/生产）" width="120" align="center">
                      <template #default="{ row: specRow }">
                        <el-input
                          v-if="specRow.isEditing"
                          v-model="specRow.batchNum"
                          size="small"
                          placeholder="批量数量（采购/生产）"
                        />
                        <span v-else>{{ specRow.batchNum }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="排序" width="80" align="center">
                      <template #default="{ row: specRow }">
                        <el-input-number
                          v-if="specRow.isEditing"
                          v-model="specRow.sort"
                          size="small"
                          :min="0"
                          :precision="0"
                          style="width: 70px"
                        />
                        <span v-else>{{ specRow.sort }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column label="首选" width="100" align="center">
                      <template #default="{ row: specRow, $index }">
                        <el-switch
                          v-model="specRow.isDefault"
                          :disabled="!specRow.isEditing"
                          active-color="#67c23a"
                          inactive-color="#dcdfe6"
                          @change="handleDefaultSpecChange(row.id, specRow, $index)"
                        />
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="130" fixed="right" align="center">
                      <template #default="{ row: specRow, $index }">
                        <div v-if="specRow.isEditing" class="action-buttons">
                          <el-button
                            size="small"
                            type="primary"
                            @click="handleSaveProductSpec(row.id, specRow, $index)"
                            >保存</el-button
                          >
                          <el-button
                            size="small"
                            @click="handleCancelProductSpecEdit(row.id, specRow, $index)"
                            >取消</el-button
                          >
                        </div>
                        <div v-else class="action-buttons">
                          <el-button
                            size="small"
                            @click="handleEditProductSpec(row.id, specRow, $index)"
                            >编辑</el-button
                          >
                          <el-button
                            size="small"
                            type="danger"
                            @click="handleDeleteProductSpec(row.id, specRow)"
                            >删除</el-button
                          >
                        </div>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              type="index"
              label="序号"
              width="60"
              align="center"
              :index="(index) => index + 1"
            />
            <el-table-column label="商品名称" min-width="80">
              <template #default="{ row, $index }">
                <el-input
                  v-if="row.isEditing"
                  v-model="row.name"
                  size="small"
                  @keydown.enter="handleSaveProduct(row, $index)"
                  ref="productNameInput"
                  placeholder="请输入商品名称"
                />
                <span v-else @dblclick="handleEditProduct(row, $index)">{{ row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="id" label="商品ID" width="90" />
            <el-table-column label="分类" width="150">
              <template #default="{ row }">
                <el-tree-select
                  v-if="row.isEditing"
                  v-model="row.categoryId"
                  :data="categoryTreeOptions"
                  :props="{
                    value: 'id',
                    label: 'name',
                    children: 'children',
                  }"
                  :selectable="(node) => node.id !== 0"
                  placeholder="请选择分类"
                  size="small"
                  style="width: 100%"
                  clearable
                  check-strictly
                  :render-after-expand="false"
                />
                <span v-else>{{ getCategoryName(row.categoryId) }}</span>
              </template>
            </el-table-column>
            <!-- <el-table-column label="排序" width="80" align="center">
              <template #default="{ row }">
                <el-input-number
                  v-if="row.isEditing"
                  v-model="row.sort"
                  size="small"
                  :min="0"
                  style="width: 70px"
                />
                <span v-else>{{ row.sort }}</span>
              </template>
            </el-table-column> -->
            <el-table-column label="操作" width="130" fixed="right" align="center">
              <template #default="{ row, $index }">
                <div v-if="row.isEditing" class="action-buttons">
                  <el-button size="small" type="primary" @click="handleSaveProduct(row, $index)"
                    >保存</el-button
                  >
                  <el-button size="small" @click="handleCancelEdit(row, $index)">取消</el-button>
                </div>
                <div v-else class="action-buttons">
                  <el-button size="small" @click.stop="handleEditProduct(row, $index)"
                    >修改</el-button
                  >
                  <el-button size="small" type="danger" @click.stop="handleDeleteProduct(row)"
                    >删除</el-button
                  >
                </div>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页组件 -->
          <div class="pagination-container">
            <el-pagination
              v-model:current-page="pagination.currentPage"
              v-model:page-size="pagination.pageSize"
              :page-sizes="pagination.pageSizes"
              :total="pagination.total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handlePageSizeChange"
              @current-change="handleCurrentPageChange"
            />
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 分类编辑对话框 -->
  <el-dialog
    v-model="categoryDialogVisible"
    :title="categoryDialogTitle"
    width="500px"
    @close="resetCategoryForm"
  >
    <el-form :model="categoryForm" :rules="categoryRules" ref="categoryFormRef" label-width="100px">
      <el-form-item label="分类名称" prop="name">
        <el-input v-model="categoryForm.name" placeholder="请输入分类名称" />
      </el-form-item>
      <el-form-item label="上级分类" prop="parentId" v-if="categoryDialogMode === 'edit'">
        <el-tree-select
          v-model="categoryForm.parentId"
          :data="categoryTreeOptions"
          :props="{ label: 'name', value: 'id', children: 'children' }"
          placeholder="请选择上级分类（不选为顶级分类）"
          check-strictly
          :render-after-expand="false"
        />
      </el-form-item>
      <!-- 新增模式下显示父级分类信息 -->
      <el-form-item label="上级分类" v-else>
        <el-input :value="getParentCategoryName(categoryForm.parentId)" disabled />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="categoryDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handleCategorySubmit">确定</el-button>
    </template>
  </el-dialog>

  <!-- 导出确认弹框 -->
  <el-dialog
    v-model="exportDialogVisible"
    title="导出商品"
    width="400px"
    :close-on-click-modal="false"
  >
    <div class="export-dialog-content">
      <el-form :model="{ exportWithSpec }" label-width="220px">
        <el-form-item label="是否一起导出商品规格">
          <el-switch v-model="exportWithSpec" active-text="是" inactive-text="否" inline-prompt />
          <div class="export-tips">
            <el-alert
              title="选择'是'将同时导出商品规格信息，选择'否'仅导出商品基本信息"
              type="info"
              :closable="false"
              show-icon
            />
          </div>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="exportDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmExport">确认导出</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onBeforeUnmount, computed, nextTick, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, ArrowLeft, ArrowRight, Download } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'

// 导入拖拽样式
import '@/styles/drag.css'

// 导入表格拖拽工具
import { inittabledrag, destroytabledrag } from '@/utils/tableDrag'

// 导入API
import {
  getCategoryTree,
  addCategory,
  updateCategory,
  deleteCategory,
  batchUpdateCategorySort,
  getProductPage,
  addProduct,
  updateProduct,
  deleteProduct,
  batchUpdateProductSort,
  getSpecsByProductId,
  addSpec,
  updateSpec,
  deleteSpec,
  batchUpdateSpecSort,
  exportProduct,
  type ProductCategory,
  type Product,
  type ProductSpec,
  type ProductCategoryAddDTO,
  type ProductCategoryUpdateDTO,
  type ProductCategorySortDTO,
  type ProductAddDTO,
  type ProductUpdateDTO,
  type ProductSpecAddDTO,
  type ProductSpecUpdateDTO,
  type ProductSpecSortDTO,
  type ProductSortDTO,
} from '@/api/sale'

const route = useRoute()
const router = useRouter()

// 响应式数据
const activeTab = ref('product')
const categoryTreeRef = ref()
const categoryFormRef = ref<FormInstance>()
const productDetailFormRef = ref<FormInstance>()
const productTableRef = ref() // 商品表格引用

const productNameInput = ref()

// 分割布局相关
const leftPanelWidth = ref(300) // 左侧分类面板宽度
const middlePanelWidth = ref(600) // 中间商品管理面板宽度
const rightPanelWidth = ref(300) // 右侧规格面板宽度
const isLeftResizing = ref(false) // 是否正在调整左侧分割线
const minPanelWidth = 300 // 最小面板宽度

// 分类相关数据
const categoryTreeData = ref<ProductCategory[]>([])
const selectedCategory = ref<ProductCategory | null>(null)
const categoryDialogVisible = ref(false)
const categoryDialogMode = ref<'add' | 'edit'>('add')

// 分类拖拽相关数据
const draggingNode = ref<any>(null)
const dropNode = ref<any>(null)
const dropType = ref<string>('')

// 商品相关数据
const productList = ref<Product[]>([])
const productLoading = ref(false)

// 查询表单数据
const searchForm = reactive({
  name: '',
  id: '',
})

// 商品拖拽相关数据
const draggingRow = ref<any>(null)
const dropRow = ref<any>(null)
const dropPosition = ref<string>('')

// 拖拽实例
const productTableSortable = ref<any>(null)
const specTableSortable = ref<any>(null) // 规格表格拖拽实例

// 展开行规格相关数据// 商品规格相关数据
const productSpecsMap = ref<Map<number, ProductSpec[]>>(new Map())
const productSpecsLoadingMap = ref<Map<number, boolean>>(new Map())

// 展开行状态管理
const expandedRows = ref<Set<number>>(new Set())

// 当前选中的商品行
const selectedProductId = ref<number | null>(null) // 商品ID到规格加载状态的映射

// 旧规格面板相关数据（待清理）
const specList = ref<ProductSpec[]>([]) // 规格列表
const specLoading = ref(false) // 规格加载状态

// 导出相关数据
const exportDialogVisible = ref(false)
const exportWithSpec = ref(false)

// 分类表单
const categoryForm = reactive<ProductCategoryAddDTO & { id?: number }>({
  name: '',
  parentId: 0,
})

// 分类表单验证规则
const categoryRules: FormRules = {
  name: [{ required: true, message: '请输入分类名称', trigger: 'blur' }],
}

// 计算属性
const categoryDialogTitle = computed(() => {
  return categoryDialogMode.value === 'add' ? '新增分类' : '编辑分类'
})

const categoryTreeOptions = computed(() => {
  const addRootOption = (data: ProductCategory[]): any[] => {
    const result: any[] = [{ id: 0, name: '顶级分类', children: [] }]
    data.forEach((item) => {
      result[0].children.push({
        id: item.id,
        name: item.name,
        children: item.children ? transformChildren(item.children) : [],
      })
    })
    return result
  }

  const transformChildren = (data: ProductCategory[]): any[] => {
    return data.map((item) => ({
      id: item.id,
      name: item.name,
      children: item.children ? transformChildren(item.children) : [],
    }))
  }

  return addRootOption(categoryTreeData.value)
})

/**
 * 扁平化分类数据，用于下拉选择框
 */
const flattenCategories = computed(() => {
  const flatten = (categories: ProductCategory[]): any[] => {
    const result: any[] = []

    const traverse = (items: ProductCategory[], prefix = '') => {
      items.forEach((item) => {
        const displayName = prefix ? `${prefix} > ${item.name}` : item.name
        result.push({
          id: item.id,
          name: displayName,
        })

        if (item.children && item.children.length > 0) {
          traverse(item.children, displayName)
        }
      })
    }

    traverse(categories)
    return result
  }

  return flatten(categoryTreeData.value)
})

// 基础方法
const loadCategoryTree = async () => {
  try {
    const response = await getCategoryTree()
    if (response.code === 1) {
      categoryTreeData.value = response.data
    } else {
      ElMessage.error(response.msg || '获取分类树失败')
    }
  } catch (error: any) {
    ElMessage.error(error.message || '获取分类树失败')
  }
}

// 分页相关响应式变量
const pagination = ref({
  currentPage: 1,
  pageSize: 20,
  total: 0,
  pageSizes: [10, 20, 50, 100],
})

const loadProductList = async (categoryId?: number) => {
  productLoading.value = true
  try {
    const response = await getProductPage({
      page: pagination.value.currentPage,
      size: pagination.value.pageSize,
      categoryId: categoryId,
      name: searchForm.name || undefined,
      id: searchForm.id ? parseInt(searchForm.id) : undefined,
    })
    if (response.code === 1) {
      productList.value = response.data.rows.map((item) => ({
        ...item,
        isEditing: false,
        originalData: { ...item },
      }))

      // 更新分页总数
      pagination.value.total = response.data.total
    } else {
      ElMessage.error(response.msg || '获取商品列表失败')
    }
  } catch (error: any) {
    ElMessage.error(error.message || '获取商品列表失败')
  } finally {
    productLoading.value = false

    // 重新初始化商品表格拖拽功能
    await nextTick()
    initProductTableDrag()
  }
}

const loadSpecList = async (productId: number, skipDragInit = false) => {
  specLoading.value = true
  try {
    const response = await getSpecsByProductId(productId)
    if (response.code === 1) {
      // 获取后端返回的最大sort值
      const maxSort =
        response.data.length > 0
          ? Math.max(
              ...response.data.map((item) =>
                item.sort !== null && item.sort !== undefined && !isNaN(item.sort) ? item.sort : -1,
              ),
            )
          : -1

      // 处理规格数据，确保sort值正确初始化
      const processedData = response.data.map((item, index) => ({
        ...item,
        sort:
          item.sort !== null && item.sort !== undefined && !isNaN(item.sort)
            ? item.sort
            : maxSort + 1 + index,
        isEditing: false,
        originalData: { ...item },
      }))

      // 按sort字段排序，确保显示顺序正确
      specList.value = processedData.sort((a, b) => a.sort - b.sort)

      console.log(
        '规格列表加载完成，sort值:',
        specList.value.map((s) => ({ id: s.id, name: s.name, sort: s.sort })),
      )
    } else {
      ElMessage.error(response.msg || '获取规格列表失败')
    }
  } catch (error: any) {
    ElMessage.error(error.message || '获取规格列表失败')
  } finally {
    specLoading.value = false

    // 只在需要时才重新初始化规格表格拖拽功能
    if (!skipDragInit) {
      await nextTick()
      initSpecTableDrag()
    }
  }
}

/**
 * 查询商品列表
 */
const handleSearch = () => {
  pagination.value.currentPage = 1
  loadProductList(selectedCategory.value?.id)
}

/**
 * 重置查询表单
 */
const resetSearch = () => {
  searchForm.name = ''
  searchForm.id = ''
  pagination.value.currentPage = 1
  loadProductList(selectedCategory.value?.id)
}

const getCategoryName = (categoryId: number): string => {
  const findCategory = (categories: ProductCategory[], id: number): string => {
    for (const category of categories) {
      if (category.id === id) {
        return category.name
      }
      if (category.children) {
        const result = findCategory(category.children, id)
        if (result) return result
      }
    }
    return ''
  }
  return findCategory(categoryTreeData.value, categoryId) || '未分类'
}

/**
 * 获取分类完整路径
 * @param categoryId 分类ID
 * @returns 分类路径字符串，如 "父类/子类/商品"
 */
const getCategoryPath = (categoryId: number): string => {
  // 构建分类路径
  const buildPath = (
    categories: ProductCategory[],
    targetId: number,
    currentPath: string = '',
  ): string => {
    for (const category of categories) {
      // 构建当前路径
      const newPath = currentPath ? `${currentPath}/${category.name}` : category.name

      if (category.id === targetId) {
        return newPath
      }

      if (category.children) {
        const result = buildPath(category.children, targetId, newPath)
        if (result) return result
      }
    }
    return ''
  }

  const path = buildPath(categoryTreeData.value, categoryId)
  return path || '全部商品'
}

/**
 * 查找第一个非顶级分类
 * @param categories 分类数据
 * @returns 第一个非顶级分类，如果没有则返回null
 */
const findFirstNonRootCategory = (categories: ProductCategory[]): ProductCategory | null => {
  for (const category of categories) {
    // 如果当前分类不是顶级分类（parentId不为0），则返回
    if (category.parentId !== 0) {
      return category
    }
    // 如果有子分类，递归查找
    if (category.children && category.children.length > 0) {
      const found = findFirstNonRootCategory(category.children)
      if (found) {
        return found
      }
    }
  }
  return null
}

// 分类相关方法
/**
 * 处理分页页码变化
 * @param currentPage 新的页码
 */
const handleCurrentPageChange = (currentPage: number) => {
  pagination.value.currentPage = currentPage
  loadProductList(selectedCategory.value?.id)
}

/**
 * 处理每页显示条数变化
 * @param pageSize 新的每页条数
 */
const handlePageSizeChange = (pageSize: number) => {
  pagination.value.pageSize = pageSize
  pagination.value.currentPage = 1 // 重置到第一页
  loadProductList(selectedCategory.value?.id)
}

/**
 * 处理分类点击事件
 * 点击分类时加载商品列表
 */
const handleCategoryClick = (data: ProductCategory) => {
  selectedCategory.value = data
  // 设置树形组件的选中状态，确保高亮显示
  nextTick(() => {
    categoryTreeRef.value?.setCurrentKey(data.id)
  })
  // 重置分页到第一页
  pagination.value.currentPage = 1
  pagination.value.total = 0
  loadProductList(data.id)
}

const handleAddCategory = () => {
  categoryDialogMode.value = 'add'
  resetCategoryForm()
  categoryDialogVisible.value = true
}

/**
 * 判断节点是否允许拖拽
 * @param draggingNode 拖拽节点
 * @returns 是否允许拖拽
 */
const allowDrag = (draggingNode: any) => {
  // 顶级分类不允许拖拽
  return draggingNode.data.id !== 0
}

/**
 * 判断节点是否允许放置
 * @param draggingNode 拖拽节点
 * @param dropNode 目标节点
 * @param type 放置类型
 * @returns 是否允许放置
 */
const allowDrop = (draggingNode: any, dropNode: any, type: string) => {
  // 不能拖拽到顶级分类内部
  if (type === 'inner' && dropNode.data.id === 0) {
    return false
  }

  // 不能拖拽到顶级分类位置（即不能成为顶级父类）
  if (dropNode.data.id === 0 && (type === 'before' || type === 'after')) {
    return false
  }

  // 不能拖拽到自己的子节点
  if (isDescendant(draggingNode.data, dropNode.data)) {
    return false
  }

  return true
}

/**
 * 判断节点A是否是节点B的子孙节点
 * @param nodeA 节点A
 * @param nodeB 节点B
 * @returns 是否是子孙节点
 */
const isDescendant = (nodeA: ProductCategory, nodeB: ProductCategory): boolean => {
  if (nodeB.children && nodeB.children.length > 0) {
    for (const child of nodeB.children) {
      if (child.id === nodeA.id || isDescendant(nodeA, child)) {
        return true
      }
    }
  }
  return false
}

/**
 * 处理节点拖拽结束事件
 * @param draggingNodeParam 拖拽节点
 * @param dropNodeParam 目标节点
 * @param dropTypeParam 放置类型
 * @param ev 事件对象
 */
const handleNodeDragEnd = (
  draggingNodeParam: any,
  dropNodeParam: any,
  dropTypeParam: string,
  ev: Event,
) => {
  // 打印被拖拽修改的行信息
  console.log('=== 拖拽操作信息 ===')
  console.log('拖拽节点:', {
    id: draggingNodeParam.data.id,
    name: draggingNodeParam.data.name,
    parentId: draggingNodeParam.data.parentId,
  })
  console.log('目标节点:', {
    id: dropNodeParam.data.id,
    name: dropNodeParam.data.name,
    parentId: dropNodeParam.data.parentId,
  })
  console.log('放置类型:', dropTypeParam)
  console.log('=================')

  // 保存拖拽状态到响应式变量
  draggingNode.value = draggingNodeParam
  dropNode.value = dropNodeParam
  dropType.value = dropTypeParam

  // 重新构建分类树结构，确保父类和子类作为一个整体移动
  rebuildCategoryTree()

  // 构建分类排序数据并更新
  buildCategorySortData()
}

/**
 * 重新构建分类树结构
 * 当拖拽父类时，确保父类和其子类作为一个整体移动
 */
const rebuildCategoryTree = () => {
  if (!draggingNode.value || !dropNode.value || !dropType.value) {
    return
  }

  const draggingId = draggingNode.value.data.id
  const dropId = dropNode.value.data.id
  const dropTypeVal = dropType.value

  console.log('=== 重新构建分类树 ===')
  console.log('拖拽节点ID:', draggingId)
  console.log('目标节点ID:', dropId)
  console.log('放置类型:', dropTypeVal)

  // 深拷贝当前分类树数据
  const newTreeData = JSON.parse(JSON.stringify(categoryTreeData.value))

  // 查找并移除拖拽节点（包括其子节点）
  const removedNode = findAndRemoveNode(newTreeData, draggingId)

  if (removedNode) {
    // 根据放置类型重新插入节点
    insertNode(newTreeData, removedNode, dropId, dropTypeVal)

    // 更新分类树数据
    categoryTreeData.value = newTreeData

    console.log('重新构建后的分类树:', newTreeData)
  }

  console.log('=================')
}

/**
 * 查找并移除节点
 * @param tree 分类树
 * @param nodeId 要移除的节点ID
 * @returns 被移除的节点
 */
const findAndRemoveNode = (tree: ProductCategory[], nodeId: number): ProductCategory | null => {
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i]
    if (node.id === nodeId) {
      return tree.splice(i, 1)[0]
    }

    if (node.children && node.children.length > 0) {
      const found = findAndRemoveNode(node.children, nodeId)
      if (found) {
        return found
      }
    }
  }
  return null
}

/**
 * 插入节点到指定位置
 * @param tree 分类树
 * @param nodeToInsert 要插入的节点
 * @param targetId 目标节点ID
 * @param insertType 插入类型
 */
const insertNode = (
  tree: ProductCategory[],
  nodeToInsert: ProductCategory,
  targetId: number,
  insertType: string,
) => {
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i]

    if (node.id === targetId) {
      // 额外保护：不能插入到顶级位置（即不能成为顶级父类）
      if (node.parentId === 0 && (insertType === 'before' || insertType === 'after')) {
        console.warn('不允许将节点插入到顶级位置')
        return
      }

      if (insertType === 'before') {
        tree.splice(i, 0, nodeToInsert)
        // 如果插入到顶级分类的子节点前面，更新父ID
        if (node.parentId === 0) {
          nodeToInsert.parentId = 0
        }
      } else if (insertType === 'after') {
        tree.splice(i + 1, 0, nodeToInsert)
        // 如果插入到顶级分类的子节点后面，更新父ID
        if (node.parentId === 0) {
          nodeToInsert.parentId = 0
        }
      } else if (insertType === 'inner') {
        if (!node.children) {
          node.children = []
        }
        node.children.push(nodeToInsert)
        // 更新插入节点的父ID
        nodeToInsert.parentId = node.id
      }
      return
    }

    if (node.children && node.children.length > 0) {
      insertNode(node.children, nodeToInsert, targetId, insertType)
    }
  }
}

/**
 * 构建分类排序数据
 * 递归遍历分类树，构建排序数据
 * 当拖拽父类时，保持父类与子类的关系不变
 */
const buildCategorySortData = () => {
  const sortData: ProductCategorySortDTO[] = []

  // 深拷贝分类树数据，避免修改原始数据
  const clonedTreeData = JSON.parse(JSON.stringify(categoryTreeData.value))

  // 递归遍历函数
  const traverse = (categories: ProductCategory[], parentId: number = 0) => {
    categories.forEach((category, index) => {
      sortData.push({
        id: category.id,
        parentId: parentId,
        sort: index,
      })

      if (category.children && category.children.length > 0) {
        traverse(category.children, category.id)
      }
    })
  }

  traverse(clonedTreeData)

  // 打印构建的排序数据
  console.log('=== 分类排序数据 ===')
  console.log('排序数据数组:', sortData)
  console.log('排序数据详情:')
  sortData.forEach((item, index) => {
    console.log(`${index + 1}. ID: ${item.id}, 父ID: ${item.parentId}, 排序: ${item.sort}`)
  })
  console.log('=================')

  // 批量更新分类排序
  updateCategorySort(sortData)
}

/**
 * 批量更新分类排序
 * @param sortData 排序数据
 */
const updateCategorySort = async (sortData: ProductCategorySortDTO[]) => {
  try {
    const response = await batchUpdateCategorySort(sortData)
    if (response.code === 1) {
      ElMessage.success('分类排序更新成功')
      // 重新加载分类树
      loadCategoryTree()
    } else {
      ElMessage.error(response.msg || '分类排序更新失败')
      // 恢复原始数据
      loadCategoryTree()
    }
  } catch (error: any) {
    ElMessage.error(error.message || '分类排序更新失败')
    // 恢复原始数据
    loadCategoryTree()
  }
}

/**
 * 新增子分类
 * @param parentData 父分类数据
 */
const handleAddSubCategory = (parentData: ProductCategory) => {
  categoryDialogMode.value = 'add'
  resetCategoryForm()
  // 使用当前点击的分类作为父级分类
  categoryForm.parentId = parentData.id
  categoryDialogVisible.value = true
}

/**
 * 获取父级分类名称
 * @param parentId 父级分类ID
 * @returns 父级分类名称
 */
const getParentCategoryName = (parentId: number): string => {
  if (parentId === 0) {
    return '顶级分类'
  }

  const findCategoryName = (categories: ProductCategory[], targetId: number): string => {
    for (const category of categories) {
      if (category.id === targetId) {
        return category.name
      }
      if (category.children && category.children.length > 0) {
        const result = findCategoryName(category.children, targetId)
        if (result) {
          return result
        }
      }
    }
    return ''
  }

  return findCategoryName(categoryTreeData.value, parentId) || '未找到父级分类'
}

const handleEditCategory = (data: ProductCategory) => {
  categoryDialogMode.value = 'edit'
  Object.assign(categoryForm, {
    id: data.id,
    name: data.name,
    parentId: data.parentId,
  })
  categoryDialogVisible.value = true
}

const handleDeleteCategory = async (data: ProductCategory) => {
  try {
    await ElMessageBox.confirm(`确定要删除分类 "${data.name}" 吗？`, '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    const response = await deleteCategory(data.id)
    if (response.code === 1) {
      ElMessage.success('删除成功')
      loadCategoryTree()
    } else {
      ElMessage.error(response.msg || '删除失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '删除失败')
    }
  }
}

const handleCategorySubmit = async () => {
  try {
    await categoryFormRef.value?.validate()

    if (categoryDialogMode.value === 'add') {
      const response = await addCategory(categoryForm)
      if (response.code === 1) {
        ElMessage.success('新增成功')
        categoryDialogVisible.value = false
        loadCategoryTree()
      } else {
        ElMessage.error(response.msg || '新增失败')
      }
    } else {
      const response = await updateCategory(categoryForm as ProductCategoryUpdateDTO)
      if (response.code === 1) {
        ElMessage.success('更新成功')
        categoryDialogVisible.value = false
        loadCategoryTree()
      } else {
        ElMessage.error(response.msg || '更新失败')
      }
    }
  } catch (error: any) {
    console.error('表单验证失败:', error)
  }
}

const resetCategoryForm = () => {
  Object.assign(categoryForm, {
    name: '',
    parentId: 0,
  })
  categoryFormRef.value?.resetFields()
}

/**
 * 添加商品 - 在表格中添加新的可编辑行
 */
const handleAddProduct = () => {
  // 获取当前选中的分类ID，如果没有选中则传0
  const categoryId = selectedCategory.value?.id || 0

  // 创建新的商品对象
  const newProduct = {
    id: 0, // 临时ID，表示新商品
    name: '',
    categoryId: categoryId,
    sort: productList.value.length + 1,
    isEditing: true, // 设置为编辑状态
    isNew: true, // 标记为新商品
    isSaving: false, // 防止重复保存
    originalData: null, // 原始数据备份
  }

  // 将新商品添加到列表开头
  productList.value.unshift(newProduct)

  // 等待DOM更新后聚焦到商品名称输入框
  nextTick(() => {
    const inputs = productNameInput.value
    if (inputs && inputs[0]) {
      inputs[0].focus()
    }
  })
}

const handleEditProduct = (row: any, index: number) => {
  row.originalData = { ...row }
  row.isEditing = true

  nextTick(() => {
    const inputs = productNameInput.value
    if (inputs && inputs[index]) {
      inputs[index].focus()
    }
  })
}

const handleSaveProduct = async (row: any, index: number) => {
  if (!row.name.trim()) {
    ElMessage.error('商品名称不能为空')
    return
  }

  // 防止重复保存
  if (row.isSaving) {
    return
  }
  row.isSaving = true

  try {
    if (row.isNew) {
      const data: ProductAddDTO = {
        name: row.name,
        categoryId: row.categoryId || 0,
      }
      const response = await addProduct(data)
      if (response.code === 1) {
        ElMessage.success('添加成功')
        // 重新加载商品列表，保持当前分类和分页
        loadProductList(selectedCategory.value?.id)
      } else {
        ElMessage.error(response.msg || '添加失败')
      }
    } else {
      const data: ProductUpdateDTO = {
        id: row.id,
        name: row.name,
        categoryId: row.categoryId,
        sort: row.sort || 0,
      }
      const response = await updateProduct(data)
      if (response.code === 1) {
        ElMessage.success('保存成功')
        row.isEditing = false
        row.originalData = null
        // 重新加载商品列表，保持当前分类和分页
        loadProductList(selectedCategory.value?.id)
      } else {
        ElMessage.error(response.msg || '保存失败')
      }
    }
  } catch (error: any) {
    ElMessage.error(error.message || '操作失败')
  } finally {
    row.isSaving = false
  }
}

const handleCancelEdit = (row: any, index: number) => {
  if (row.isNew) {
    productList.value.splice(index, 1)
  } else {
    Object.assign(row, row.originalData)
    row.isEditing = false
    row.originalData = null
  }
}

const handleDeleteProduct = async (row: Product) => {
  try {
    await ElMessageBox.confirm(`确定要删除商品 "${row.name}" 吗？`, '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    const response = await deleteProduct(row.id)
    if (response.code === 1) {
      ElMessage.success('删除成功')
      // 如果当前页只有一条数据且不是第一页，则回到上一页
      if (productList.value.length === 1 && pagination.value.currentPage > 1) {
        pagination.value.currentPage--
      }
      // 重新加载商品列表，保持当前分类和分页
      loadProductList(selectedCategory.value?.id)
    } else {
      ElMessage.error(response.msg || '删除失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '删除失败')
    }
  }
}

/**
 * 管理商品规格
 * 点击管理规格时展开对应商品的规格详情（展开行方式）
 */
const handleManageSpecs = (row: Product) => {
  // 现在使用展开行方式，不需要设置selectedProduct
  // 规格详情会在展开行中显示
  // 展开对应商品的规格行
  if (productTableRef.value) {
    productTableRef.value.toggleRowExpansion(row, true)
  }
}

/**
 * 初始化商品表格拖拽功能
 */
const initProductTableDrag = () => {
  try {
    // 等待DOM更新完成
    nextTick(() => {
      // 销毁之前的拖拽实例（如果存在）
      if (productTableSortable.value) {
        destroytabledrag(productTableSortable.value)
        productTableSortable.value = null
      }

      // 初始化新的拖拽实例
      productTableSortable.value = inittabledrag(
        '.product-table',
        (oldIndex: number, newIndex: number) => {
          console.log('商品拖拽结束:', { oldIndex, newIndex })

          // 获取拖拽的商品数据
          const draggedProduct = productList.value[oldIndex]
          const targetProduct = productList.value[newIndex]

          if (draggedProduct && targetProduct) {
            draggingRow.value = draggedProduct
            dropRow.value = targetProduct
            dropPosition.value = newIndex > oldIndex ? 'after' : 'before'

            console.log('商品拖拽结束:')
            console.log('- 拖拽商品:', draggedProduct.name, 'ID:', draggedProduct.id)
            console.log('- 目标商品:', targetProduct.name, 'ID:', targetProduct.id)
            console.log('- 放置位置:', dropPosition.value)

            // 重新构建商品列表排序
            rebuildProductList()

            // 构建排序数据并更新
            buildProductSortData()
          }
        },
        {
          animation: 150,
          ghostClass: 'sortable-ghost',
          chosenClass: 'sortable-chosen',
          dragClass: 'sortable-drag',
        },
      )

      // 启用悬停滚动功能，增加滚动条区域检测精度
      const productTable = document.querySelector('.product-table .el-table__body-wrapper')
      if (productTable) {
        enableHoverScroll(productTable as HTMLElement, 30)
      }

      console.log('商品表格拖拽功能初始化成功')
    })
  } catch (error) {
    console.error('初始化商品表格拖拽功能失败:', error)
  }
}

/**
 * 销毁商品表格拖拽功能
 */
const destroyProductTableDrag = () => {
  if (productTableSortable.value) {
    destroytabledrag(productTableSortable.value)
    productTableSortable.value = null
    console.log('商品表格拖拽功能已销毁')
  }
}

/**
 * 初始化规格表格拖拽功能
 * 参考用户管理的实现方式
 */
const initSpecTableDrag = async () => {
  try {
    await nextTick()
    await nextTick() // 确保DOM完全渲染

    if (specList.value.length > 0) {
      // 销毁之前的拖拽实例（如果存在）
      if (specTableSortable.value) {
        destroytabledrag(specTableSortable.value)
        specTableSortable.value = null
      }

      // 查找规格表格元素
      const specTable = document.querySelector('.spec-table')
      if (specTable) {
        // 初始化新的拖拽实例，直接传递表格元素
        specTableSortable.value = inittabledrag(
          specTable as HTMLElement,
          async (oldIndex: number, newIndex: number) => {
            await handleSpecDragEnd(oldIndex, newIndex)
          },
          {
            animation: 150,
            ghostClass: 'sortable-ghost',
            chosenClass: 'sortable-chosen',
            dragClass: 'sortable-drag',
          },
        )

        // 启用悬停滚动功能，增加滚动条区域检测精度
        const specTableBody = specTable.querySelector('.el-table__body-wrapper')
        if (specTableBody) {
          enableHoverScroll(specTableBody as HTMLElement, 30)
        }

        console.log('规格表格拖拽功能初始化成功')
      } else {
        console.error('未找到规格表格元素')
      }
    } else {
      console.log('规格列表为空，跳过拖拽初始化')
    }
  } catch (error) {
    console.error('初始化规格表格拖拽功能失败:', error)
  }
}

/**
 * 销毁规格表格拖拽功能
 */
const destroySpecTableDrag = () => {
  if (specTableSortable.value) {
    destroytabledrag(specTableSortable.value)
    specTableSortable.value = null
    console.log('规格表格拖拽功能已销毁')
  }
}

/**
 * 处理规格拖拽结束逻辑 - 实现插入式排序
 * 参考用户管理的拖拽排序实现，确保排序值连续且合理
 * @param oldIndex 原始位置索引
 * @param newIndex 新位置索引
 */
const handleSpecDragEnd = async (oldIndex: number, newIndex: number) => {
  if (oldIndex === newIndex) return

  try {
    console.log('规格拖拽:', { oldIndex, newIndex })

    // 设置拖拽操作标志位，防止watch监听器重复初始化
    isDragOperation.value = true

    // 1. 复制当前数据并调整顺序
    const newData = [...specList.value]
    // 从原位置移除
    const [movedItem] = newData.splice(oldIndex, 1)
    // 插入新位置
    newData.splice(newIndex, 0, movedItem)

    // 2. 重新计算排序值 - 使用简单的连续排序
    const updatedData = newData.map((item, index) => ({
      ...item,
      sort: index, // 简单的从0开始的连续排序
    }))

    console.log(
      '更新后的规格排序:',
      updatedData.map((s) => ({ id: s.id, name: s.name, sort: s.sort })),
    )

    // 4. 更新数据
    specList.value = updatedData

    // 5. 保存排序变化
    await saveSpecSortOrder(updatedData)

    // 6. 重置标志位
    isDragOperation.value = false
  } catch (error) {
    console.error('处理规格拖拽结束失败:', error)
    ElMessage.error('规格拖拽排序失败')
    // 恢复原始数据
    isDragOperation.value = false // 重置标志位
    // 注意：现在使用展开行方式，不需要恢复特定商品的规格数据
  }
}

/**
 * 保存规格拖拽排序到后端
 * 参考用户管理的保存排序实现
 * @param specs 规格数组（可选，默认为整个specList）
 */
const saveSpecSortOrder = async (specs?: ProductSpec[]) => {
  try {
    // 构建排序数据：只包含传入的规格（默认为整个specList）
    const targetSpecs = specs || specList.value
    const sortData: ProductSpecSortDTO[] = targetSpecs.map((spec) => ({
      id: spec.id,
      sort: spec.sort, // 使用规格实际的sort字段值
    }))
    console.log('规格排序数据:', sortData)

    // 调用API保存排序
    const response = await batchUpdateSpecSort(sortData)
    if (response.code === 1) {
      ElMessage.success('规格排序保存成功')
      // 不重新加载数据，保持当前排序状态
    } else {
      ElMessage.error(response.msg || '规格排序保存失败')
      // 只在失败时才恢复原始数据
      // 注意：现在使用展开行方式，不需要恢复特定商品的规格数据
    }
  } catch (error) {
    console.error('保存规格排序失败:', error)
    ElMessage.error('规格排序保存失败')
    // 只在失败时才恢复原始数据
    // 注意：现在使用展开行方式，不需要恢复特定商品的规格数据
  }
}

/**
 * 重新构建商品列表排序
 * 根据拖拽结果重新排列商品顺序
 */
const rebuildProductList = () => {
  if (!draggingRow.value || !dropRow.value || !dropPosition.value) {
    return
  }

  // 深拷贝商品列表
  const productListCopy = JSON.parse(JSON.stringify(productList.value))

  // 查找并移除拖拽的商品
  const draggingIndex = productListCopy.findIndex(
    (item: Product) => item.id === draggingRow.value.id,
  )
  if (draggingIndex === -1) return

  const [draggedItem] = productListCopy.splice(draggingIndex, 1)

  // 查找目标位置
  const dropIndex = productListCopy.findIndex((item: Product) => item.id === dropRow.value.id)
  if (dropIndex === -1) return

  // 根据放置类型插入到目标位置
  if (dropPosition.value === 'before') {
    productListCopy.splice(dropIndex, 0, draggedItem)
  } else if (dropPosition.value === 'after') {
    productListCopy.splice(dropIndex + 1, 0, draggedItem)
  }

  // 更新商品列表
  productList.value = productListCopy

  console.log('商品列表重新排序完成')
}

/**
 * 构建商品排序数据
 * 递归遍历商品列表，构建排序数据
 */
const buildProductSortData = () => {
  const sortData: ProductSortDTO[] = []

  // 遍历商品列表
  productList.value.forEach((product, index) => {
    sortData.push({
      id: product.id,
      sort: index,
    })
  })

  // 打印构建的排序数据
  console.log('=== 商品排序数据 ===')
  console.log('排序数据数组:', sortData)
  console.log('排序数据详情:')
  sortData.forEach((item, index) => {
    console.log(`${index + 1}. ID: ${item.id}, 排序: ${item.sort}`)
  })
  console.log('=================')

  // 批量更新商品排序
  updateProductSort(sortData)
}

/**
 * 批量更新商品排序
 * @param sortData 排序数据
 */
const updateProductSort = async (sortData: ProductSortDTO[]) => {
  try {
    const response = await batchUpdateProductSort(sortData)
    if (response.code === 1) {
      ElMessage.success('商品排序更新成功')
      // 重新加载商品列表
      loadProductList(selectedCategory.value?.id)
    } else {
      ElMessage.error(response.msg || '商品排序更新失败')
      // 恢复原始数据
      loadProductList(selectedCategory.value?.id)
    }
  } catch (error: any) {
    ElMessage.error(error.message || '商品排序更新失败')
    // 恢复原始数据
    loadProductList(selectedCategory.value?.id)
  }
}

/**
 * 处理商品行点击事件
 * 点击商品时展开规格详情（展开行方式）
 */
const handleProductRowClick = (row: Product) => {
  console.log('点击商品行:', row.id, row.name)
  // 现在使用展开行方式，不需要设置selectedProduct
  // 规格详情会在展开行中显示
}

// 获取商品行样式类名
const getProductRowClassName = ({ row }: { row: Product }) => {
  // 现在使用展开行方式，不需要选中商品的高亮显示
  return 'product-row-clickable'
}

// 分割线拖拽相关方法
const startLeftResize = (e: MouseEvent) => {
  isLeftResizing.value = true
  const startX = e.clientX
  const startLeftWidth = leftPanelWidth.value
  const containerWidth = window.innerWidth - 40 // 减去padding

  const handleMouseMove = (e: MouseEvent) => {
    const deltaX = e.clientX - startX
    const newLeftWidth = startLeftWidth + deltaX
    const remainingWidth = containerWidth - newLeftWidth - 16 // 减去两个分割线宽度
    const newMiddleWidth = middlePanelWidth.value
    const newRightWidth = remainingWidth - newMiddleWidth

    if (
      newLeftWidth >= minPanelWidth &&
      newRightWidth >= minPanelWidth &&
      newMiddleWidth >= minPanelWidth
    ) {
      leftPanelWidth.value = newLeftWidth
      rightPanelWidth.value = newRightWidth
    }
  }

  const handleMouseUp = () => {
    isLeftResizing.value = false
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
    document.body.style.cursor = 'default'
    document.body.style.userSelect = 'auto'
  }

  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
  document.body.style.cursor = 'col-resize'
  document.body.style.userSelect = 'none'
}

// 窗口大小改变时调整面板宽度
const handleWindowResize = () => {
  const containerWidth = window.innerWidth - 40
  const totalPanelWidth = leftPanelWidth.value + middlePanelWidth.value
  const leftRatio = leftPanelWidth.value / totalPanelWidth
  const middleRatio = middlePanelWidth.value / totalPanelWidth

  leftPanelWidth.value = Math.max(minPanelWidth, containerWidth * leftRatio)
  middlePanelWidth.value = Math.max(minPanelWidth, containerWidth - leftPanelWidth.value - 16)
}

/**
 * 处理展开行规格管理
 * @param row 商品行数据
 * @param expandedRows 展开的行数组
 */
/**
 * 处理展开行变化
 * @param row 商品行数据
 * @param expandedRows 展开的行数据
 */
const handleExpandChange = async (row: Product, expandedRowsParam: Product[]) => {
  const isExpanded = expandedRowsParam.some((expandedRow) => expandedRow.id === row.id)

  if (isExpanded) {
    // 添加到展开状态集合
    expandedRows.value.add(row.id)
    // 加载该商品的规格列表
    await loadProductSpecs(row.id)
    
    // 等待DOM更新后为规格表格启用悬停滚动功能
    await nextTick()
    await nextTick()
    
    // 为展开的规格表格启用悬停滚动功能，增加滚动条区域检测精度
    const specTables = document.querySelectorAll(`.spec-table-${row.id} .el-table__body-wrapper`)
    specTables.forEach((table) => {
      enableHoverScroll(table as HTMLElement, 30)
    })
  } else {
    // 从展开状态集合中移除
    expandedRows.value.delete(row.id)
    // 清理该商品的规格数据
    productSpecsMap.value.delete(row.id)
    productSpecsLoadingMap.value.delete(row.id)
  }
}

/**
 * 启用悬停滚动功能
 * 增加滚动条区域检测精度，避免误触拖拽功能
 * @param element 需要启用滚动的元素
 * @param threshold 滚动检测阈值（像素）
 */
const enableHoverScroll = (element: HTMLElement, threshold: number = 20) => {
  if (!element) return

  let isScrolling = false
  let scrollTimeout: number

  // 监听鼠标移动事件
  element.addEventListener('mousemove', (e: MouseEvent) => {
    const rect = element.getBoundingClientRect()
    const scrollTop = element.scrollTop
    const scrollHeight = element.scrollHeight
    const clientHeight = element.clientHeight
    
    // 检查是否在顶部滚动区域
    const isNearTop = scrollTop > 0 && e.clientY - rect.top <= threshold
    
    // 检查是否在底部滚动区域
    const isNearBottom = 
      scrollTop < scrollHeight - clientHeight && 
      rect.bottom - e.clientY <= threshold
    
    // 如果在滚动区域且没有正在滚动
    if ((isNearTop || isNearBottom) && !isScrolling) {
      isScrolling = true
      
      // 清除之前的超时
      if (scrollTimeout) {
        clearTimeout(scrollTimeout)
      }
      
      // 设置滚动速度
      const scrollSpeed = 5
      
      const scroll = () => {
        if (isNearTop) {
          element.scrollTop = Math.max(0, element.scrollTop - scrollSpeed)
        } else if (isNearBottom) {
          element.scrollTop = Math.min(
            scrollHeight - clientHeight,
            element.scrollTop + scrollSpeed
          )
        }
        
        // 继续滚动直到鼠标离开滚动区域
        if (isScrolling) {
          scrollTimeout = window.setTimeout(scroll, 16)
        }
      }
      
      scroll()
    }
  })
  
  // 监听鼠标离开事件
  element.addEventListener('mouseleave', () => {
    isScrolling = false
    if (scrollTimeout) {
      clearTimeout(scrollTimeout)
    }
  })
  
  // 监听鼠标松开事件
  element.addEventListener('mouseup', () => {
    isScrolling = false
    if (scrollTimeout) {
      clearTimeout(scrollTimeout)
    }
  })
}

/**
 * 处理商品行点击事件
 * @param row 商品行数据
 */
const handleRowClick = (row: Product) => {
  selectedProductId.value = row.id
}

/**
 * 获取行样式类名
 * @param row 行数据
 * @returns 样式类名
 */
const getRowClassName = (row: Product) => {
  return selectedProductId.value === row.id ? 'selected-product-row' : ''
}

/**
 * 加载商品规格列表
 * @param productId 商品ID
 */
const loadProductSpecs = async (productId: number) => {
  productSpecsLoadingMap.value.set(productId, true)
  try {
    const response = await getSpecsByProductId(productId)
    if (response.code === 1) {
      const specs = response.data.map((spec: any) => ({
        ...spec,
        isEditing: false,
        originalData: null,
        isSaving: false,
      }))
      productSpecsMap.value.set(productId, specs)
    } else {
      ElMessage.error(response.msg || '获取规格列表失败')
    }
  } catch (error: any) {
    ElMessage.error(error.message || '获取规格列表失败')
  } finally {
    productSpecsLoadingMap.value.delete(productId)
  }
}

/**
 * 添加商品规格
 * @param productId 商品ID
 */
const handleAddProductSpec = (productId: number) => {
  const specs = productSpecsMap.value.get(productId) || []
  const newSpec: any = {
    productId,
    name: '',
    unit: '',
    conversionNum: 1,
    price: 0,
    barcode: '',
    volume: 0,
    weight: 0,
    perBoxNum: 0,
    alertInventory: 0,
    batchNum: 0,
    isDefault: false,
    sort: specs.length,
    isEditing: true,
    isNew: true,
    originalData: null,
    isSaving: false,
  }
  specs.push(newSpec)
  productSpecsMap.value.set(productId, specs)
}

/**
 * 编辑商品规格
 * @param productId 商品ID
 * @param row 规格行数据
 * @param index 规格行索引
 */
const handleEditProductSpec = (productId: number, row: any, index: number) => {
  row.originalData = { ...row }
  row.isEditing = true
}

/**
 * 保存商品规格
 * @param productId 商品ID
 * @param row 规格行数据
 * @param index 规格行索引
 */
const handleSaveProductSpec = async (productId: number, row: any, index: number) => {
  // 验证规格名称不能为空
  if (!row.name.trim()) {
    ElMessage.error('规格名称不能为空')
    return
  }

  // 验证单位不能为空
  if (!row.unit.trim()) {
    ElMessage.error('单位不能为空')
    return
  }

  // 验证换算数量必须大于0
  if (!row.conversionNum || row.conversionNum <= 0) {
    ElMessage.error('换算数量必须大于0')
    return
  }

  // 验证价格必须大于等于0
  if (row.price === undefined || row.price === null || row.price < 0) {
    ElMessage.error('价格必须大于等于0')
    return
  }

  // 防止重复保存
  if (row.isSaving) {
    return
  }
  row.isSaving = true

  try {
    if (row.isNew) {
      const data: ProductSpecAddDTO = {
        productId,
        name: row.name,
        unit: row.unit || '',
        conversionNum: row.conversionNum || 1,
        price: row.price || 0,
        barcode: row.barcode || '',
        volume: row.volume || 0,
        weight: row.weight || 0,
        perBoxNum: row.perBoxNum || 0,
        alertInventory: row.alertInventory || 0,
        batchNum: row.batchNum || 0,
        isDefault: row.isDefault || false,
      }
      const response = await addSpec(data)
      if (response.code === 1) {
        ElMessage.success('添加成功')
        await loadProductSpecs(productId)
      } else {
        ElMessage.error(response.msg || '添加失败')
      }
    } else {
      const data: ProductSpecUpdateDTO = {
        id: row.id,
        productId,
        name: row.name,
        unit: row.unit || '',
        conversionNum: row.conversionNum || 1,
        price: row.price || 0,
        barcode: row.barcode || '',
        volume: row.volume || 0,
        weight: row.weight || 0,
        perBoxNum: row.perBoxNum || 0,
        alertInventory: row.alertInventory || 0,
        batchNum: row.batchNum || 0,
        isDefault: row.isDefault || false,
        sort: row.sort || 0,
      }
      const response = await updateSpec(data)
      if (response.code === 1) {
        ElMessage.success('保存成功')
        row.isEditing = false
        row.originalData = null
      } else {
        ElMessage.error(response.msg || '保存失败')
      }
    }
  } catch (error: any) {
    ElMessage.error(error.message || '操作失败')
  } finally {
    row.isSaving = false
  }
}

/**
 * 取消编辑商品规格
 * @param productId 商品ID
 * @param row 规格行数据
 * @param index 规格行索引
 */
const handleCancelProductSpecEdit = (productId: number, row: any, index: number) => {
  const specs = productSpecsMap.value.get(productId) || []
  if (row.isNew) {
    specs.splice(index, 1)
    productSpecsMap.value.set(productId, specs)
  } else {
    Object.assign(row, row.originalData)
    row.isEditing = false
    row.originalData = null
  }
}

/**
 * 删除商品规格
 * @param productId 商品ID
 * @param row 规格行数据
 */
const handleDeleteProductSpec = async (productId: number, row: ProductSpec) => {
  try {
    await ElMessageBox.confirm(`确定要删除规格 "${row.name}" 吗？`, '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    const response = await deleteSpec(row.id)
    if (response.code === 1) {
      ElMessage.success('删除成功')
      await loadProductSpecs(productId)
    } else {
      ElMessage.error(response.msg || '删除失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '删除失败')
    }
  }
}

/**
 * 处理默认规格开关变更
 * @param productId 商品ID
 * @param row 规格行数据
 * @param index 规格行索引
 */
const handleDefaultSpecChange = async (productId: number, row: any, index: number) => {
  const specs = productSpecsMap.value.get(productId) || []
  const previousDefaultSpecs: any[] = []

  if (row.isDefault) {
    // 如果设置为默认，找到其他默认规格并记录
    specs.forEach((spec, idx) => {
      if (idx !== index && spec.isDefault) {
        previousDefaultSpecs.push(spec)
        spec.isDefault = false
      }
    })

    // 调用接口更新当前规格为默认
    try {
      const currentData: ProductSpecUpdateDTO = {
        id: row.id,
        productId,
        name: row.name,
        unit: row.unit || '',
        conversionNum: row.conversionNum || 1,
        price: row.price || 0,
        barcode: row.barcode || '',
        volume: row.volume || 0,
        weight: row.weight || 0,
        perBoxNum: row.perBoxNum || 0,
        alertInventory: row.alertInventory || 0,
        batchNum: row.batchNum || 0,
        isDefault: true,
        sort: row.sort || 0,
      }
      const currentResponse = await updateSpec(currentData)
      if (currentResponse.code !== 1) {
        ElMessage.error(currentResponse.msg || '设置首选失败')
        row.isDefault = false
        return
      }

      // 调用接口更新之前默认的规格为非默认
      for (const prevSpec of previousDefaultSpecs) {
        const prevData: ProductSpecUpdateDTO = {
          id: prevSpec.id,
          productId,
          name: prevSpec.name,
          unit: prevSpec.unit || '',
          conversionNum: prevSpec.conversionNum || 1,
          price: prevSpec.price || 0,
          barcode: prevSpec.barcode || '',
          volume: prevSpec.volume || 0,
          weight: prevSpec.weight || 0,
          perBoxNum: prevSpec.perBoxNum || 0,
          alertInventory: prevSpec.alertInventory || 0,
          batchNum: prevSpec.batchNum || 0,
          isDefault: false,
          sort: prevSpec.sort || 0,
        }
        const prevResponse = await updateSpec(prevData)
        if (prevResponse.code !== 1) {
          ElMessage.error(prevResponse.msg || '取消首选失败')
        }
      }

      ElMessage.success('设置首选成功')
    } catch (error: any) {
      ElMessage.error(error.message || '操作失败')
      row.isDefault = false
    }
  } else {
    // 如果取消默认，直接调用接口更新
    try {
      const data: ProductSpecUpdateDTO = {
        id: row.id,
        productId,
        name: row.name,
        unit: row.unit || '',
        conversionNum: row.conversionNum || 1,
        price: row.price || 0,
        barcode: row.barcode || '',
        volume: row.volume || 0,
        weight: row.weight || 0,
        perBoxNum: row.perBoxNum || 0,
        alertInventory: row.alertInventory || 0,
        batchNum: row.batchNum || 0,
        isDefault: false,
        sort: row.sort || 0,
      }
      const response = await updateSpec(data)
      if (response.code === 1) {
        ElMessage.success('取消首选成功')
      } else {
        ElMessage.error(response.msg || '操作失败')
        row.isDefault = true
      }
    } catch (error: any) {
      ElMessage.error(error.message || '操作失败')
      row.isDefault = true
    }
  }
}

// 生命周期
onMounted(async () => {
  await loadCategoryTree()

  // 默认选择第一个分类（排除顶级分类）
  if (categoryTreeData.value.length > 0) {
    const firstCategory = findFirstNonRootCategory(categoryTreeData.value)
    if (firstCategory) {
      selectedCategory.value = firstCategory
      await loadProductList(firstCategory.id)

      // 设置树形组件的当前选中节点
      await nextTick()
      if (categoryTreeRef.value) {
        categoryTreeRef.value.setCurrentKey(firstCategory.id)
      }
    }
  }

  // 初始化面板宽度根据屏幕宽度调整
  const containerWidth = window.innerWidth - 40
  if (containerWidth > 1400) {
    leftPanelWidth.value = 300
    middlePanelWidth.value = 600
  }

  // 监听窗口大小变化
  window.addEventListener('resize', handleWindowResize)

  // 初始化商品表格拖拽功能
  await nextTick()
  initProductTableDrag()
})

// 增加一个标志位来控制是否需要重新初始化拖拽功能
const isDragOperation = ref(false)

/**
 * 监听规格列表变化，重新初始化拖拽功能
 * 参考用户管理的实现方式
 */
watch(
  () => productSpecsMap.value,
  async (newMap) => {
    // 如果是拖拽操作导致的数据变化，则不重新初始化拖拽功能
    if (isDragOperation.value) {
      isDragOperation.value = false
      return
    }

    // 为每个展开的商品初始化规格表格拖拽功能
    for (const [productId, specs] of newMap) {
      if (specs.length > 0) {
        await nextTick()
        await nextTick()
        try {
          // 初始化规格表格拖拽功能
          const specTable = document.querySelector(`.spec-table-${productId}`)
          if (specTable) {
            // 这里可以添加规格表格的拖拽初始化逻辑
            // initSpecTableDrag(specTable, productId)
          }
        } catch (error) {
          console.error('初始化规格表格拖拽功能失败:', error)
        }
      }
    }
  },
  { deep: true },
)

/**
 * 处理导出商品按钮点击事件
 * 显示导出确认弹框
 */
const handleExportProduct = () => {
  exportDialogVisible.value = true
  exportWithSpec.value = false // 默认不导出规格
}

/**
 * 确认导出商品
 * 根据用户选择调用导出API并下载文件
 */
const confirmExport = async () => {
  try {
    // 构建导出参数
    const params: any = {
      isSpec: exportWithSpec.value,
    }

    // 添加查询条件参数
    if (searchForm.name) {
      params.name = searchForm.name
    }
    console.log('导出参数:', params)
    if (searchForm.id) {
      params.id = parseInt(searchForm.id)
    }
    if (selectedCategory.value?.id) {
      params.categoryId = selectedCategory.value.id
    }

    // 调用导出API
    const blob = await exportProduct(params)

    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url

    // 设置文件名
    const timestamp = new Date()
      .toISOString()
      .slice(0, 19)
      .replace(/[:\-T]/g, '')
    const fileName = `商品数据_${exportWithSpec.value ? '含规格' : '不含规格'}_${timestamp}.xlsx`
    link.download = fileName

    // 触发下载
    document.body.appendChild(link)
    link.click()

    // 清理资源
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('导出成功')
    exportDialogVisible.value = false
  } catch (error: any) {
    ElMessage.error(error.message || '导出失败')
  }
}

// 组件卸载时移除监听器

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleWindowResize)

  // 销毁商品表格拖拽功能
  destroyProductTableDrag()
  
  // 清理enableHoverScroll事件监听器
  document.querySelectorAll('[data-hover-scroll-enabled]').forEach(element => {
    const cleanup = (element as any)._hoverScrollCleanup
    if (cleanup) {
      cleanup()
    }
  })
})
  
  

</script>

<style scoped>
.product-management {
  height: 100vh;
  padding: 20px;
  background-color: #f5f5f5;
}

/* 两列布局 */
.two-column-layout {
  display: flex;
  height: 100%;
  gap: 0;
}

/* 左侧面板 - 商品分类 */
.left-panel {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 右侧面板 - 商品管理 */
.right-panel {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  flex: 1;
}

/* 分割线 */
.splitter {
  width: 8px;
  cursor: col-resize;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: transparent;
  z-index: 10;
}

.splitter:hover .splitter-line,
.splitter-active .splitter-line {
  background-color: #409eff;
  width: 3px;
}

.splitter-line {
  width: 1px;
  height: 100%;
  background-color: #dcdfe6;
  transition: all 0.2s;
}

/* 面板标题 */
.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #ebeef5;
  background: #fafafa;
  flex-shrink: 0;
}

.panel-header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.total-count {
  font-size: 12px;
  color: #909399;
}

.category-info {
  font-size: 13px;
  color: #606266;
  background: #e6f7ff;
  padding: 2px 8px;
  border-radius: 4px;
}

/* 分类树 */
.category-tree {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.tree-node {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding-right: 8px;
}

.node-label {
  flex: 1;
}

.node-actions {
  display: none;
}

.tree-node:hover .node-actions {
  display: flex;
  gap: 4px;
  align-items: center;
}

/* 禁用按钮样式 */
.node-actions .el-button.is-disabled {
  color: #c0c4cc;
  cursor: not-allowed;
  opacity: 0.6;
}

.node-actions .el-button:not(.is-disabled) {
  color: #409eff;
}

.node-actions .el-button:not(.is-disabled):hover {
  color: #66b1ff;
}

/* 表格容器 */
.table-container {
  flex: 1;
  overflow: auto;
  padding: 8px;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 确保flex容器能够正确收缩 */
}

/* 商品表格自适应高度 */
.product-table {
  height: auto !important;
  max-height: none !important;
}

/* 分页容器 */
.pagination-container {
  height: 52px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 0;
  border-top: 1px solid #ebeef5;
  background-color: #fff;
}

/* 规格详情面板的表格容器 - 允许水平滚动 */
.spec-detail-panel .table-container {
  overflow: hidden;
  padding: 8px;
  display: block;
  position: relative;
}

/* 规格表格包装器 - 支持水平滚动 */
.spec-table-wrapper {
  width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  display: block;
  position: relative;
  min-height: 200px;
}

/* 规格表格样式 */
.spec-table {
  width: 1500px;
  min-width: 1500px;
}

/* 固定列样式 */
.spec-table .el-table__fixed-right {
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
  z-index: 10;
  position: sticky !important;
  right: 0 !important;
  background: #fff;
  height: 100% !important;
  min-height: 40px;
  top: 0 !important;
}

.spec-table .el-table__fixed-right::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 1px;
  background-color: #ebeef5;
  z-index: 1;
}

.spec-table .el-table__fixed-right .el-table__cell {
  background-color: #fff !important;
  border-left: 1px solid #ebeef5;
  position: relative !important;
  z-index: 2;
  height: auto !important;
}

/* 确保表格体高度正确 */
.spec-table .el-table__body-wrapper {
  overflow: visible !important;
}

/* 增强固定列显示 */
.spec-table .el-table__fixed-right {
  transform: none !important;
  will-change: auto !important;
}

.spec-table-wrapper::-webkit-scrollbar {
  height: 12px;
  display: block !important;
}

.spec-table-wrapper::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 6px;
  margin: 0 4px;
}

.spec-table-wrapper::-webkit-scrollbar-thumb {
  background: #909399;
  border-radius: 6px;
  border: 2px solid #f1f1f1;
}

.spec-table-wrapper::-webkit-scrollbar-thumb:hover {
  background: #606266;
}

.spec-table-wrapper::-webkit-scrollbar-corner {
  background: #f1f1f1;
}

/* 强制显示滚动条 */
.spec-table-wrapper {
  scrollbar-width: thin;
  scrollbar-color: #909399 #f1f1f1;
}

/* 规格详情面板 */
.spec-detail-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 空状态 */
.empty-panel {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 4px;
}

/* 查询表单 */
.search-form-inline {
  margin-left: 60px;
  display: flex;
  align-items: center;
}

.search-form-inline .el-form--inline {
  display: flex;
  align-items: center;
}

.search-form-inline .el-form--inline .el-form-item {
  margin-right: 12px;
  margin-bottom: 0;
  display: flex;
  align-items: center;
}

.search-form-inline .el-form--inline .el-form-item:last-child {
  margin-right: 0;
}

/* 确保表单标签和输入框垂直对齐 */
.search-form-inline .el-form-item__label {
  line-height: 32px;
  height: 32px;
}

.search-form-inline .el-input {
  height: 32px;
  line-height: 32px;
}

.search-form-inline .el-input__inner {
  height: 32px;
  line-height: 32px;
}

.search-form-inline .el-button {
  height: 32px;
  line-height: 32px;
  padding: 0 15px;
}

/* 调整header-left布局以包含查询表单 */
.header-left {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
  align-items: center; /* 确保垂直居中对齐 */
}

/* 调整header-right布局确保按钮对齐 */
.header-right {
  display: flex;
  align-items: center;
}

.header-right .el-button {
  height: 32px;
  line-height: 32px;
  padding: 0 15px;
}

/* 确保标题和查询表单垂直对齐 */
.header-left h4 {
  margin: 0;
  line-height: 32px; /* 与表单控件高度对齐 */
}

.header-left span {
  line-height: 32px; /* 与表单控件高度对齐 */
}

/* 商品行点击样式 */
.product-row-clickable {
  cursor: pointer;
}

.product-row-clickable:hover {
  background-color: #f5f7fa !important;
}

/* 展开图标动画 */
.product-table :deep(.el-table__expand-icon) {
  transition: transform 0.3s ease;
  cursor: pointer;
}

.product-table :deep(.el-table__expand-icon.el-table__expand-icon--expanded) {
  transform: rotate(90deg);
}

/* 展开行过渡动画 */
.product-table :deep(.el-table__expanded-cell) {
  animation: expandIn 0.3s ease-out;
}

@keyframes expandIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 提高选择器优先级确保高亮样式不被覆盖 */
.el-table .selected-product-row {
  background-color: #e6f7ff !important;
  color: #1890ff !important;
  font-weight: 600 !important;
  border-left: 3px solid #1890ff !important;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.1) !important;
}

.el-table .selected-product-row:hover {
  background-color: #bae7ff !important;
  color: #0050b3 !important;
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.2) !important;
}

/* 表格行内编辑样式 */
.el-table .el-input,
.el-table .el-input-number {
  border: none;
  background: transparent;
}

.el-table .el-input .el-input__wrapper {
  box-shadow: none;
  background: transparent;
}

.el-table .el-input .el-input__wrapper:hover,
.el-table .el-input .el-input__wrapper:focus {
  box-shadow: 0 0 0 1px var(--el-color-primary) inset;
}

/* 树形组件优化 */
.el-tree {
  background: transparent;
}

.el-tree .el-tree-node__content {
  height: 32px;
}

.el-tree .el-tree-node__content:hover {
  background-color: #f5f7fa;
}

.el-tree .el-tree-node.is-current > .el-tree-node__content {
  background-color: #e6f7ff;
  color: #1890ff;
  font-weight: 600;
  border-left: 3px solid #1890ff;
}

.el-tree .el-tree-node.is-current > .el-tree-node__content:hover {
  background-color: #bae7ff;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .product-management {
    padding: 10px;
  }

  .left-panel {
    min-width: 200px;
  }

  .right-panel {
    min-width: 400px;
  }
}

@media (max-width: 1024px) {
  .two-column-layout {
    flex-direction: column;
    gap: 10px;
  }

  .splitter {
    display: none;
  }

  .left-panel,
  .right-panel {
    width: 100% !important;
    height: 50%;
  }
}

@media (max-width: 768px) {
  .left-panel,
  .right-panel {
    height: auto;
    min-height: 300px;
  }
}

/* 规格展开容器样式 - 增加滚动条区域容错空间 */
.spec-expand-container {
  padding: 8px;
  background-color: #fafafa;
  border-radius: 4px;
  margin: 8px 0;
}

/* 规格表格样式 - 增加滚动条区域容错空间 */
.spec-expand-table {
  margin: 0 -8px; /* 抵消容器的padding，保持表格对齐 */
}

.spec-expand-table .el-table__body-wrapper {
  padding: 4px; /* 为滚动条区域增加容错空间 */
}

/* 规格名称容器样式 */
.spec-name-container {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
}

.spec-name {
  flex: 1;
}

.default-tag {
  flex-shrink: 0;
  font-size: 10px;
  padding: 1px 4px;
  height: 16px;
  line-height: 14px;
}

.default-tag-inline {
  flex-shrink: 0;
  font-size: 10px;
  padding: 1px 4px;
  height: 16px;
  line-height: 14px;
  margin-left: 4px;
}

/* 规格编辑容器样式 */
.spec-edit-container {
  display: flex;
  align-items: center;
  gap: 6px;
  width: 100%;
}

.default-checkbox {
  flex-shrink: 0;
  font-size: 12px;
}

/* 规格表格拖拽样式 */
.spec-table .sortable-ghost {
  opacity: 0.4;
  background-color: #e6f7ff !important;
}

.spec-table .sortable-chosen {
  background-color: #bae7ff !important;
  color: #1890ff;
  font-weight: 600;
}

.spec-table .sortable-drag {
  background-color: #ffffff !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  opacity: 0.8;
  cursor: move;
}

/* 拖拽时的行样式 */
.spec-table .el-table__row.sortable-ghost td {
  background-color: #e6f7ff !important;
  border-color: #91d5ff !important;
}

.spec-table .el-table__row.sortable-chosen td {
  background-color: #bae7ff !important;
  border-color: #409eff !important;
  color: #1890ff;
}

.spec-table .el-table__row.sortable-drag td {
  background-color: #ffffff !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  opacity: 0.8;
}

/* 整行拖拽样式 - 参考用户管理 */
.spec-table :deep(.el-table__row) {
  cursor: move;
  transition: all 0.3s ease;
}

.spec-table :deep(.el-table__row:hover) {
  background-color: rgba(64, 158, 255, 0.05) !important;
}

/* 拖拽时的样式 */
.spec-table :deep(.sortable-ghost) {
  opacity: 0.4;
  background-color: #f5f7fa !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 拖拽时的禁止样式 */
.spec-table .el-table__row.sortable-ghost .el-input,
.spec-table .el-table__row.sortable-ghost .el-input-number,
.spec-table .el-table__row.sortable-ghost .el-button {
  pointer-events: none;
  opacity: 0.5;
}

/* 拖拽时的表格样式优化 */
.spec-table.sortable-dragging {
  pointer-events: none;
}

.spec-table.sortable-dragging .el-table__body-wrapper {
  overflow: visible !important;
}

/* 导出弹框样式 */
.export-dialog-content {
  padding: 20px 0;
}

.export-tips {
  margin-top: 10px;
}

.export-tips .el-alert {
  padding: 8px 12px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
