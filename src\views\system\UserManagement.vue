<template>
  <div class="user-management">
    <el-card>
      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :model="searchForm" inline>
          <el-form-item label="用户名">
            <el-input
              v-model="searchForm.username"
              placeholder="请输入用户名"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="岗位">
            <el-input
              v-model="searchForm.position"
              placeholder="请输入岗位"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="状态">
            <el-select
              v-model="searchForm.status"
              placeholder="请选择状态"
              clearable
              style="width: 120px"
            >
              <el-option label="在职" value="1" />
              <el-option label="离职" value="0" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="loadUserList">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 操作按钮 -->
      <div class="toolbar" style="display: flex; align-items: center; gap: 10px">
        <el-button type="primary" @click="showAddDialog">
          <el-icon><Plus /></el-icon>
          新增用户
        </el-button>
        <el-button type="danger" @click="handleBatchDelete" :disabled="selectedUsers.length === 0">
          批量删除
        </el-button>
        <el-button type="info" @click="handleExport">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
        <el-upload
          ref="uploadRef"
          :action="''"
          :auto-upload="false"
          :on-change="handleImport"
          :show-file-list="false"
          accept=".xlsx,.xls"
        >
          <el-button type="primary">
            <el-icon><Upload /></el-icon>
            导入
          </el-button>
        </el-upload>
        <el-button type="warning" @click="handleDownloadTemplate"> 下载模板 </el-button>
      </div>
      <!-- 用户表格 -->
      <el-table
        ref="tableRef"
        :data="userList"
        style="width: 100%"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        row-key="id"
      >
        <el-table-column type="selection" width="55" />

        <el-table-column type="index" label="序号" width="80" />
        <el-table-column prop="username" label="用户名" min-width="120" sortable="custom" />
        <el-table-column prop="position" label="岗位" min-width="120" sortable="custom" />
        <el-table-column prop="status" label="状态" width="100" align="center" sortable="custom">
          <template #default="{ row }">
            <el-tag :type="row.status === '1' ? 'success' : 'danger'">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" align="center" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button type="primary" size="small" @click="showEditDialog(row)">编辑</el-button>
              <el-button type="warning" size="small" @click="showPermissionDialog(row)"
                >菜单权限</el-button
              >
              <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadUserList"
          @current-change="loadUserList"
          :prev-text="'上一页'"
          :next-text="'下一页'"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="500px" @close="resetForm">
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="80px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="formData.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="!isEdit">
          <el-input
            v-model="formData.password"
            type="password"
            placeholder="请输入密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="岗位" prop="position">
          <el-input v-model="formData.position" placeholder="请输入岗位" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio value="1">在职</el-radio>
            <el-radio value="0">离职</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting"> 确定 </el-button>
      </template>
    </el-dialog>

    <!-- 菜单权限设置对话框 -->
    <el-dialog title="菜单权限设置" v-model="permissionDialogVisible" width="600px">
      <div v-if="currentUser">
        <p>
          为用户 <strong>{{ currentUser.username }}</strong> 分配菜单权限：
        </p>
        <el-tree
          ref="menuTreeRef"
          :data="menuTreeData"
          :props="{ label: 'menuName', children: 'children' }"
          node-key="id"
          show-checkbox
          default-expand-all
          :default-checked-keys="userMenuIds"
        />
      </div>
      <template #footer>
        <el-button @click="permissionDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handlePermissionSubmit" :loading="permissionSubmitting">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onBeforeUnmount, nextTick, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, Download, Upload, View } from '@element-plus/icons-vue'
import { useRoute } from 'vue-router'
import { usePageStatePersist } from '@/stores/pageState'
import { downloadExcelFile, handleDownloadError } from '@/utils/downloadHelper'
import { inittabledrag, destroytabledrag } from '@/utils/tableDrag'
import {
  getUserPage,
  getUserById,
  addUser,
  updateUser,
  deleteUser,
  batchDeleteUsers,
  exportUsers,
  downloadTemplate,
  importUsers,
  getMenuTree,
  getUserMenuIds,
  assignUserMenus,
  updateUserSortOrder,
  type User,
  type UserAddDTO,
  type UserUpdateDTO,
  type UserPageQuery,
  type PageResult,
  type SysMenu,
  type UserMenuAssignDTO,
  type UserSortUpdateDTO,
} from '@/api/system/user'
import type { FormInstance, UploadInstance, UploadFile } from 'element-plus'

// 页面状态管理
const route = useRoute()
const { saveCurrentState, restoreState } = usePageStatePersist(route.path)

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const permissionSubmitting = ref(false)
const dialogVisible = ref(false)
const permissionDialogVisible = ref(false)
const userList = ref<User[]>([])
const selectedUsers = ref<User[]>([])
const menuTreeData = ref<SysMenu[]>([])
const userMenuIds = ref<number[]>([])
const currentUser = ref<User | null>(null)
const isEdit = ref(false)
const dialogTitle = ref('新增用户')

// 表单引用
const formRef = ref<FormInstance>()
const uploadRef = ref<UploadInstance>()
const menuTreeRef = ref()
const tableRef = ref() // 表格引用，用于拖拽功能
const sortableInstance = ref<any>(null) // 存储Sortable实例

// 搜索表单
const searchForm = reactive<UserPageQuery>({
  username: '',
  position: '',
  status: '',
})

// 分页数据
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0,
})

// 排序数据
const sortData = reactive({
  sortField: '',
  sortOrder: '',
})

// 保存页面状态
const savePageState = () => {
  const state = {
    searchForm: { ...searchForm },
    pagination: { ...pagination },
    sortData: { ...sortData },
    userList: [...userList.value],
    selectedUsers: [...selectedUsers.value],
    loading: loading.value,
  }
  saveCurrentState(state)
}

// 恢复页面状态
const restorePageState = () => {
  const savedState = restoreState()
  if (savedState) {
    Object.assign(searchForm, savedState.searchForm)
    Object.assign(pagination, savedState.pagination)
    Object.assign(sortData, savedState.sortData || { sortField: '', sortOrder: '' })
    userList.value = savedState.userList || []
    selectedUsers.value = savedState.selectedUsers || []
    // 恢复状态时确保loading为false
    loading.value = false
    return true
  }
  return false
}

// 表单数据
const formData = reactive<UserAddDTO & { id?: number }>({
  username: '',
  password: '',
  position: '',
  status: '1',
})

// 表单验证规则
const formRules = {
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  position: [{ required: true, message: '请输入岗位', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }],
}

// 加载用户列表
const loadUserList = async () => {
  try {
    loading.value = true
    const params: UserPageQuery = {
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize,
      ...searchForm,
      // 添加排序参数
      sortField: sortData.sortField,
      sortOrder: sortData.sortOrder,
    }

    const response = await getUserPage(params)
    if (response.code === 1) {
      userList.value = response.data.rows
      pagination.total = response.data.total
      // 保存页面状态
      savePageState()
    } else {
      ElMessage.error(response.msg || '获取用户列表失败')
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

// 重置搜索
const resetSearch = () => {
  Object.assign(searchForm, {
    username: '',
    position: '',
    status: '',
  })
  pagination.pageNum = 1
  loadUserList()
}

// 显示新增对话框
const showAddDialog = () => {
  isEdit.value = false
  dialogTitle.value = '新增用户'
  resetForm()
  dialogVisible.value = true
}

// 显示编辑对话框
const showEditDialog = async (row: User) => {
  try {
    isEdit.value = true
    dialogTitle.value = '编辑用户'

    const response = await getUserById(row.id)
    if (response.code === 1) {
      Object.assign(formData, {
        id: response.data.id,
        username: response.data.username,
        position: response.data.position,
        status: response.data.status,
        password: '', // 编辑时不显示密码
      })
      dialogVisible.value = true
    } else {
      ElMessage.error(response.msg || '获取用户信息失败')
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    ElMessage.error('获取用户信息失败')
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    let response
    if (isEdit.value) {
      // 编辑用户
      const updateData: UserUpdateDTO = {
        id: formData.id!,
        username: formData.username,
        position: formData.position,
        status: formData.status,
      }
      response = await updateUser(updateData)
    } else {
      // 新增用户
      const addData: UserAddDTO = {
        username: formData.username,
        password: formData.password,
        position: formData.position,
        status: formData.status,
      }
      response = await addUser(addData)
    }

    if (response.code === 1) {
      ElMessage.success(isEdit.value ? '更新成功' : '新增成功')
      dialogVisible.value = false
      loadUserList()
    } else {
      ElMessage.error(response.msg || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    id: undefined,
    username: '',
    password: '',
    position: '',
    status: '1',
  })
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 删除用户
const handleDelete = async (row: User) => {
  try {
    await ElMessageBox.confirm(`确定要删除用户 "${row.username}" 吗？`, '删除确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    const response = await deleteUser(row.id)
    if (response.code === 1) {
      ElMessage.success('删除成功')
      loadUserList()
    } else {
      ElMessage.error(response.msg || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除用户失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 批量删除
const handleBatchDelete = async () => {
  if (selectedUsers.value.length === 0) {
    ElMessage.warning('请选择要删除的用户')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedUsers.value.length} 个用户吗？`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    const ids = selectedUsers.value.map((user) => user.id)
    const response = await batchDeleteUsers(ids)
    if (response.code === 1) {
      ElMessage.success('批量删除成功')
      selectedUsers.value = []
      loadUserList()
    } else {
      ElMessage.error(response.msg || '批量删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

// 导出用户数据
const handleExport = async () => {
  try {
    const params: UserPageQuery = {
      ...searchForm,
    }

    const blob = await exportUsers(params)
    const filename = `用户数据_${new Date().toISOString().slice(0, 10)}`
    downloadExcelFile(blob, filename)
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    const errorMessage = await handleDownloadError(error, '导出失败')
    ElMessage.error(errorMessage)
  }
}

// 下载导入模板
const handleDownloadTemplate = async () => {
  try {
    const blob = await downloadTemplate()
    downloadExcelFile(blob, '用户导入模板')
    ElMessage.success('模板下载成功')
  } catch (error) {
    console.error('下载模板失败:', error)
    const errorMessage = await handleDownloadError(error, '下载模板失败')
    ElMessage.error(errorMessage)
  }
}

// 导入用户数据
const handleImport = async (file: UploadFile) => {
  if (!file.raw) return

  try {
    const response = await importUsers(file.raw)
    if (response.code === 1) {
      ElMessage.success('导入成功')
      loadUserList()
    } else {
      ElMessage.error(response.msg || '导入失败')
    }
  } catch (error) {
    console.error('导入失败:', error)
    ElMessage.error('导入失败')
  }
}

// 表格选择变化
const handleSelectionChange = (selection: User[]) => {
  selectedUsers.value = selection
  // 保存页面状态
  savePageState()
}

// 处理排序变化
const handleSortChange = ({ prop, order }: { prop: string; order: string | null }) => {
  if (order) {
    sortData.sortField = prop
    sortData.sortOrder = order === 'ascending' ? 'asc' : 'desc'
  } else {
    sortData.sortField = ''
    sortData.sortOrder = ''
  }

  // 重新加载数据
  pagination.pageNum = 1 // 排序时重置到第一页
  loadUserList()
}

// 获取状态文本
const getStatusText = (status: string) => {
  return status === '1' ? '在职' : '离职'
}

// 显示权限设置对话框
const showPermissionDialog = async (row: User) => {
  try {
    currentUser.value = row
    console.log('row:', row)

    // 获取所有菜单树数据
    const menuResponse = await getMenuTree()
    if (menuResponse.code === 1) {
      menuTreeData.value = menuResponse.data
    } else {
      ElMessage.error('获取菜单数据失败')
      return
    }

    // 获取目标用户已分配的菜单ID
    const userMenuResponse = await getUserMenuIds(row.id)
    console.log('userMenuResponse.data:', userMenuResponse.data)
    if (userMenuResponse.code === 1) {
      // **核心修改部分：过滤出叶子节点**
      const userMenuFullIds = userMenuResponse.data
      const menuLeafIds = getLeafIds(menuTreeData.value, userMenuFullIds)
      userMenuIds.value = menuLeafIds

      // **关键修复：等待DOM更新后手动设置选中状态**
      permissionDialogVisible.value = true

      // 等待对话框渲染完成
      await nextTick()

      // 手动设置el-tree的选中状态
      if (menuTreeRef.value) {
        menuTreeRef.value.setCheckedKeys(menuLeafIds)
      }
    } else {
      ElMessage.error('获取用户权限失败')
      return
    }
  } catch (error) {
    console.error('获取权限数据失败:', error)
    ElMessage.error('获取权限数据失败')
  }
}

// 辅助函数：递归遍历菜单树，获取所有叶子节点的ID，并与用户权限ID进行比对
const getLeafIds = (menus: any[], userIds: number[]) => {
  const leafIds: number[] = []
  const traverse = (nodes: any[]) => {
    for (const node of nodes) {
      if (!node.children || node.children.length === 0) {
        // 如果是叶子节点，并且用户有这个权限，就添加到列表中
        if (userIds.includes(node.id)) {
          leafIds.push(node.id)
        }
      } else {
        // 如果有子节点，递归遍历子节点
        traverse(node.children)
      }
    }
  }
  traverse(menus)
  return leafIds
}

// 提交权限设置
const handlePermissionSubmit = async () => {
  if (!currentUser.value || !menuTreeRef.value) return

  try {
    permissionSubmitting.value = true

    // 获取选中的菜单ID
    const checkedKeys = menuTreeRef.value.getCheckedKeys()
    const halfCheckedKeys = menuTreeRef.value.getHalfCheckedKeys()
    const allCheckedKeys = [...checkedKeys, ...halfCheckedKeys]

    const assignData: UserMenuAssignDTO = {
      userId: currentUser.value.id,
      menuIds: allCheckedKeys,
    }

    const response = await assignUserMenus(assignData)
    if (response.code === 1) {
      ElMessage.success('权限设置成功')
      permissionDialogVisible.value = false
    } else {
      ElMessage.error(response.msg || '权限设置失败')
    }
  } catch (error) {
    console.error('权限设置失败:', error)
    ElMessage.error('权限设置失败')
  } finally {
    permissionSubmitting.value = false
  }
}

// 保存拖拽排序到后端
const saveDragSortOrder = async (users?: any[]) => {
  try {
    // 构建排序数据：只包含传入的用户（默认为整个userList）
    const targetUsers = users || userList.value
    const sortData: UserSortUpdateDTO[] = targetUsers.map((user) => ({
      id: user.id,
      sortOrder: user.sortOrder, // 使用用户实际的sortOrder字段值
    }))
    console.log('sortData:', sortData)

    // 调用API保存排序
    const response = await updateUserSortOrder(sortData)
    if (response.code === 1) {
      ElMessage.success('排序保存成功')
      // 重新加载数据以确保数据同步
      await loadUserList()
    } else {
      ElMessage.error(response.msg || '排序保存失败')
    }
  } catch (error) {
    console.error('保存排序失败:', error)
    ElMessage.error('排序保存失败')
  }
}

/**
 * 打印当前排序
 * 用于调试和查看当前排序状态
 */
const logCurrentOrder = () => {
  console.log(
    '当前用户排序:',
    userList.value.map((user) => ({
      id: user.id,
      username: user.username,
      sortOrder: user.sortOrder,
    })),
  )
  ElMessage.info('排序信息已打印到控制台')
}

/**
 * 组件挂载时加载数据并初始化拖拽功能
 * 使用插入式排序逻辑优化拖拽体验
 */
onMounted(async () => {
  // 尝试恢复页面状态
  const hasRestoredState = restorePageState()

  // 如果没有保存的状态或者用户列表为空，则重新加载数据
  if (!hasRestoredState || userList.value.length === 0) {
    await loadUserList()
  }

  // 初始化表格拖拽功能
  await initDraggableTable()
})

/**
 * 初始化可拖拽表格
 * 使用参考代码中的插入式排序逻辑
 */
const initDraggableTable = async () => {
  await nextTick()
  await nextTick() // 确保DOM完全渲染

  if (tableRef.value && userList.value.length > 0) {
    try {
      const tableElement = tableRef.value.$el
      const tbody = tableElement?.querySelector('.el-table__body-wrapper tbody')

      if (tbody) {
        sortableInstance.value = inittabledrag(
          tableElement,
          async (oldIndex, newIndex) => {
            await handleDragEnd(oldIndex, newIndex)
          },
          {
            animation: 150,
            ghostClass: 'sortable-ghost',
          },
        )
        console.log('拖拽功能初始化成功')
      } else {
        console.error('未找到表格tbody元素')
      }
    } catch (error) {
      console.error('初始化拖拽功能失败:', error)
    }
  } else {
    console.log('表格引用不存在或用户列表为空，跳过拖拽初始化')
  }
}

/**
 * 处理拖拽结束逻辑 - 实现插入式排序
 * 基于参考代码优化，确保排序值连续且合理
 * @param oldIndex 原始位置索引
 * @param newIndex 新位置索引
 */
const handleDragEnd = async (oldIndex: number, newIndex: number) => {
  if (oldIndex === newIndex) return

  try {
    // 1. 复制当前数据并调整顺序
    const newData = [...userList.value]
    // 从原位置移除
    const [movedItem] = newData.splice(oldIndex, 1)
    // 插入新位置
    newData.splice(newIndex, 0, movedItem)

    // 2. 计算当前页的起始排序值（用于分页场景）
    const pageStartSort = userList.value[0]?.sortOrder || 1

    // 3. 重新计算排序值 - 关键逻辑
    const updatedData = newData.map((item, index) => ({
      ...item,
      // 保持当前页排序值连续，基于页起始值计算
      sortOrder: pageStartSort + index,
    }))

    // 4. 更新数据
    userList.value = updatedData

    // 5. 保存排序变化
    await saveDragSortOrder(updatedData)
  } catch (error) {
    console.error('处理拖拽结束失败:', error)
    ElMessage.error('拖拽排序失败')
    // 恢复原始数据
    await loadUserList()
  }
}

/**
 * 监听用户列表变化，重新初始化拖拽功能
 * 使用优化的插入式排序逻辑
 */
watch(
  () => userList.value,
  async (newList) => {
    if (newList.length > 0 && tableRef.value) {
      await nextTick()
      await nextTick()
      try {
        // 销毁旧的拖拽实例
        if (sortableInstance.value) {
          destroytabledrag(sortableInstance.value)
          sortableInstance.value = null
        }

        // 重新初始化拖拽功能
        await initDraggableTable()
        console.log('拖拽功能重新初始化成功')
      } catch (error) {
        console.error('重新初始化拖拽功能失败:', error)
      }
    }
  },
  { deep: true },
)

/**
 * 在组件卸载前保存状态并安全销毁拖拽实例
 * 避免内存泄漏和潜在的错误
 */
onBeforeUnmount(async () => {
  try {
    savePageState()
    // 安全销毁表格拖拽实例
    if (sortableInstance.value) {
      destroytabledrag(sortableInstance.value)
      sortableInstance.value = null
    }
  } catch (error) {
    console.error('组件卸载时销毁拖拽实例失败:', error)
  }
})
</script>

<style scoped>
/* 拖拽相关样式 */
.sortable-ghost {
  opacity: 0.5;
  background-color: #e5e7eb;
}

.el-table__body-wrapper tbody tr {
  cursor: grab;
}

.el-table__body-wrapper tbody tr.sortable-ghost {
  cursor: grabbing;
}

.search-area {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.toolbar {
  margin-bottom: 20px;
}

.toolbar .el-button {
  margin-right: 10px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  align-items: center;
  white-space: nowrap;
}

.action-buttons .el-button {
  margin: 0;
}

/* 整行拖拽样式 */
:deep(.el-table__row) {
  cursor: move;
  transition: all 0.3s ease;
}

:deep(.el-table__row:hover) {
  background-color: rgba(64, 158, 255, 0.05) !important;
}

/* 拖拽时的样式 */
:deep(.sortable-ghost) {
  opacity: 0.4;
  background-color: #f5f7fa !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.sortable-chosen) {
  background-color: #ecf5ff !important;
  box-shadow: 0 2px 12px rgba(64, 158, 255, 0.2);
}

:deep(.sortable-drag) {
  opacity: 0.8;
  background-color: #d9ecff !important;
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.3);
  transform: rotate(2deg);
}
</style>
