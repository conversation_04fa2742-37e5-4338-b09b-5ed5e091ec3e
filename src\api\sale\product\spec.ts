// src/api/sale/product/spec.ts
import request from '../../request'
import type { ApiResult } from './category'

// 商品规格实体类型
export interface ProductSpec {
  id: number
  productId: number
  name: string
  unit?: string
  conversionNum?: number
  price?: number
  barcode?: string
  volume?: number
  weight?: number
  perBoxNum?: number
  alertInventory?: number
  batchNum?: number
  isDefault: boolean
  sort: number
}

// 商品规格新增DTO
export interface ProductSpecAddDTO {
  productId: number
  name: string
  unit?: string
  conversionNum?: number
  price?: number
  barcode?: string
  volume?: number
  weight?: number
  perBoxNum?: number
  alertInventory?: number
  batchNum?: number
  isDefault?: boolean
}

// 商品规格更新DTO
export interface ProductSpecUpdateDTO {
  id: number
  productId: number
  name: string
  unit?: string
  conversionNum?: number
  price?: number
  barcode?: string
  volume?: number
  weight?: number
  perBoxNum?: number
  alertInventory?: number
  batchNum?: number
  isDefault?: boolean
  sort?: number
}

// 根据商品ID获取规格列表
export const getSpecsByProductId = (productId: number): Promise<ApiResult<ProductSpec[]>> => {
  return request({
    url: `/sale/product/spec/product/${productId}`,
    method: 'get',
  })
}

// 根据ID获取规格
export const getSpecById = (id: number): Promise<ApiResult<ProductSpec>> => {
  return request({
    url: `/sale/product/spec/${id}`,
    method: 'get',
  })
}

// 新增规格
export const addSpec = (data: ProductSpecAddDTO): Promise<ApiResult<string>> => {
  return request({
    url: '/sale/product/spec',
    method: 'post',
    data,
  })
}

// 更新规格
export const updateSpec = (data: ProductSpecUpdateDTO): Promise<ApiResult<string>> => {
  return request({
    url: '/sale/product/spec',
    method: 'put',
    data,
  })
}

// 删除规格
export const deleteSpec = (id: number): Promise<ApiResult<string>> => {
  return request({
    url: `/sale/product/spec/${id}`,
    method: 'delete',
  })
}

// 商品规格排序DTO
export interface ProductSpecSortDTO {
  id: number
  sort: number
}

// 批量更新规格排序
export const batchUpdateSpecSort = (data: ProductSpecSortDTO[]): Promise<ApiResult<string>> => {
  return request({
    url: '/sale/product/spec/batch-update-sort',
    method: 'put',
    data,
  })
}
