# 璟果ERP管理系统

基于Vue 3 + TypeScript + Element Plus构建的现代化ERP管理系统前端应用。

## 功能特性

- 🔐 **用户认证** - 完整的登录/登出功能
- 👤 **个人中心** - 用户信息管理和编辑
- 📊 **仪表盘** - 数据统计和快捷操作
- 🎨 **响应式设计** - 适配各种屏幕尺寸
- 🔄 **状态管理** - 基于Pinia的状态管理
- 🛡️ **路由守卫** - 自动登录状态检查
- 🎯 **TypeScript** - 完整的类型支持

## 技术栈

- **前端框架**: Vue 3 (Composition API)
- **构建工具**: Vite
- **UI组件库**: Element Plus
- **状态管理**: Pinia
- **路由管理**: Vue Router 4
- **HTTP客户端**: Axios
- **开发语言**: TypeScript
- **样式**: CSS3 + 响应式设计

## API接口

系统基于提供的OpenAPI规范实现，包含以下接口：

- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出
- `GET /api/auth/current` - 获取当前用户信息

## 项目结构

```
src/
├── api/                 # API接口定义
│   ├── request.ts      # Axios配置和拦截器
│   └── user.ts         # 用户相关API
├── layout/             # 布局组件
│   └── MainLayout.vue  # 主布局组件
├── router/             # 路由配置
│   └── index.ts        # 路由定义和守卫
├── stores/             # 状态管理
│   └── counter.ts      # 用户状态管理
├── views/              # 页面组件
│   ├── auth/           # 认证相关页面
│   │   └── Login.vue   # 登录页面
│   ├── Dashboard.vue   # 仪表盘
│   └── Profile.vue     # 个人中心
└── main.ts             # 应用入口
```

## 快速开始

### 环境要求

- Node.js >= 20.19.0
- npm 或 yarn

### 安装依赖

```bash
# 使用npm
npm install

# 或使用yarn
yarn install
```

### 开发环境运行

```bash
# 使用npm
npm run dev

# 或使用yarn
yarn dev
```

应用将在 http://localhost:5173 启动

### 构建生产版本

```bash
# 使用npm
npm run build

# 或使用yarn
yarn build
```

## 使用说明

### 登录功能

1. 访问应用会自动跳转到登录页面
2. 输入用户名和密码进行登录
3. 登录成功后会跳转到仪表盘页面
4. 用户信息会保存在本地存储中

### 主要页面

- **仪表盘** (`/dashboard`) - 显示系统概览、统计数据和快捷操作
- **个人中心** (`/profile`) - 查看和编辑个人信息

### 响应式设计

- 桌面端：完整的侧边栏和顶部导航
- 平板端：自适应布局调整
- 移动端：优化的移动体验

## 配置说明

### 环境变量

在 `.env` 文件中配置：

```env
# API基础URL
VITE_APP_API_BASE_URL=http://localhost:8080/api

# 应用标题
VITE_APP_TITLE=璟果ERP管理系统
```

### API配置

在 `src/api/request.ts` 中可以修改：

- 请求超时时间
- 请求/响应拦截器
- 错误处理逻辑

## 开发指南

### 添加新页面

1. 在 `src/views/` 下创建新的Vue组件
2. 在 `src/router/index.ts` 中添加路由配置
3. 如需要认证，设置 `meta: { requiresAuth: true }`

### 添加新API

1. 在 `src/api/` 下定义接口类型和请求函数
2. 在组件中导入并使用

### 状态管理

使用Pinia进行状态管理，在 `src/stores/` 下定义store。

## 浏览器支持

- Chrome >= 87
- Firefox >= 78
- Safari >= 14
- Edge >= 88

## 许可证

MIT License
