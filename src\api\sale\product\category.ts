// src/api/sale/productCategory.ts
import request from '../../request'

// 定义接口类型
export interface ApiResult<T> {
  code: number
  msg: string
  data: T
}

// 商品分类实体类型
export interface ProductCategory {
  id: number
  name: string
  parentId: number
  sort: number
  children: ProductCategory[]
}

// 商品分类新增DTO
export interface ProductCategoryAddDTO {
  name: string
  parentId: number
}

// 商品分类更新DTO
export interface ProductCategoryUpdateDTO {
  id: number
  name: string
  parentId: number
  sort?: number
}

// 获取分类树
export const getCategoryTree = (): Promise<ApiResult<ProductCategory[]>> => {
  return request({
    url: '/sale/product/category/tree',
    method: 'get',
  })
}

// 根据ID获取分类
export const getCategoryById = (id: number): Promise<ApiResult<ProductCategory>> => {
  return request({
    url: `/sale/product/category/${id}`,
    method: 'get',
  })
}

// 新增分类
export const addCategory = (data: ProductCategoryAddDTO): Promise<ApiResult<string>> => {
  return request({
    url: '/sale/product/category',
    method: 'post',
    data,
  })
}

// 更新分类
export const updateCategory = (data: ProductCategoryUpdateDTO): Promise<ApiResult<string>> => {
  return request({
    url: '/sale/product/category',
    method: 'put',
    data,
  })
}

// 删除分类
export const deleteCategory = (id: number): Promise<ApiResult<string>> => {
  return request({
    url: `/sale/product/category/${id}`,
    method: 'delete',
  })
}

// 商品分类排序DTO
export interface ProductCategorySortDTO {
  id: number
  parentId: number
  sort: number
}

// 批量更新分类排序
export const batchUpdateCategorySort = (data: ProductCategorySortDTO[]): Promise<ApiResult<string>> => {
  return request({
    url: '/sale/product/category/batch-update-sort',
    method: 'put',
    data,
  })
}
