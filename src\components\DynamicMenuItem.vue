<template>
  <!-- 有子菜单的情况 -->
  <div class="menu-item-wrapper" ref="wrapperRef">
    <el-sub-menu
      v-if="menu.children && menu.children.length > 0"
      :index="menu.path || `menu-${menu.id}`"
      ref="menuRef"
      @mouseenter.stop="showTooltip"
      @mouseleave.stop="hideTooltip"
    >
      <template #title>
        <el-icon v-if="menu.icon">
          <component :is="getIconComponent(menu.icon)" />
        </el-icon>
        <span>{{ menu.menuName }}</span>
      </template>
      <template v-for="child in menu.children" :key="child.id">
        <DynamicMenuItem :menu="child" />
      </template>
    </el-sub-menu>

    <!-- 没有子菜单的情况 -->
    <el-menu-item
      v-else
      :index="menu.path || `menu-${menu.id}`"
      ref="menuRef"
      @mouseenter.stop="showTooltip"
      @mouseleave.stop="hideTooltip"
    >
      <el-icon v-if="menu.icon">
        <component :is="getIconComponent(menu.icon)" />
      </el-icon>
      <template #title>{{ menu.menuName }}</template>
    </el-menu-item>

    <!-- 备注气泡框 -->
    <el-popover
      v-if="permissionStore.showMenuRemarkTooltip && menu.remark"
      ref="popoverRef"
      :virtual-ref="menuRef"
      virtual-triggering
      placement="right-start"
      :width="200"
      trigger="hover"
      :hide-after="0"
      :show-arrow="false"
      :offset="8"
      :visible="isHovering"
      popper-class="menu-remark-popover"
    >
      <div class="menu-remark-content">
        <div class="remark-text">{{ menu.remark }}</div>
      </div>
    </el-popover>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import type { SysMenu } from '@/api/system/user'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import { usePermissionStore } from '@/stores/permission'
import type { PopoverInstance } from 'element-plus'

interface Props {
  menu: SysMenu
}

const props = defineProps<Props>()
const permissionStore = usePermissionStore()

// 气泡框相关
const popoverRef = ref<PopoverInstance>()
const menuRef = ref<HTMLElement>()
const wrapperRef = ref<HTMLElement>()
const isHovering = ref(false)

// 获取图标组件 - 支持所有Element Plus图标
const getIconComponent = (iconName: string) => {
  // 直接从ElementPlusIconsVue中获取图标组件
  const iconComponent = (ElementPlusIconsVue as any)[iconName]

  // 如果找不到图标，返回默认的Setting图标
  return iconComponent || ElementPlusIconsVue.Setting
}

// 组件挂载时初始化
onMounted(() => {
  // 不需要全局事件监听
})

/**
 * 显示气泡框
 * 当鼠标移入菜单项时，如果启用了备注提示且菜单有备注，则显示气泡框
 * 阻止事件冒泡防止父级菜单气泡框弹出
 */
const showTooltip = (event: MouseEvent) => {
  event.stopPropagation()
  event.preventDefault()
  if (permissionStore.showMenuRemarkTooltip && props.menu.remark) {
    // 隐藏父级气泡框
    hideParentTooltip(event)
    // 显示当前气泡框
    isHovering.value = true
  }
}

/**
 * 隐藏气泡框
 * 当鼠标移出菜单项时，隐藏气泡框
 * 阻止事件冒泡防止父级菜单气泡框异常显示
 */
const hideTooltip = (event: MouseEvent) => {
  event.stopPropagation()
  event.preventDefault()
  isHovering.value = false
}

/**
 * 隐藏父级气泡框
 * 向上查找父级菜单项并触发其mouseleave事件以隐藏气泡框
 */
const hideParentTooltip = (event: MouseEvent) => {
  // 向上查找父级菜单项
  let parentElement = event.currentTarget as HTMLElement
  while (parentElement && parentElement.parentElement) {
    parentElement = parentElement.parentElement
    // 查找父级菜单项的包装器
    const parentWrapper = parentElement.closest('.menu-item-wrapper')
    if (parentWrapper && parentWrapper !== wrapperRef.value) {
      // 找到父级菜单项，触发其mouseleave事件
      const parentMenu = parentWrapper.querySelector('.el-menu-item, .el-sub-menu')
      if (parentMenu) {
        const mouseLeaveEvent = new MouseEvent('mouseleave', {
          bubbles: true,
          cancelable: true,
          view: window,
        })
        parentMenu.dispatchEvent(mouseLeaveEvent)
      }
      break
    }
  }
}
</script>

<style>
/* 菜单项样式可以在这里自定义 */
.menu-item-wrapper {
  position: relative;
}

.menu-remark-content {
  padding: 8px 12px;
}

.remark-text {
  font-size: 13px;
  color: #ffffff;
  line-height: 1.4;
  word-break: break-word;
}

/* 气泡框样式 - 黑色背景白色字体 */
.el-popover.menu-remark-popover {
  background-color: #000000 !important;
  border: 1px solid #333333 !important;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3) !important;
}

.el-popover.menu-remark-popover .el-popper__arrow {
  border-color: #000000 !important;
}

.el-popover.menu-remark-popover .el-popper__arrow::before {
  background-color: #000000 !important;
  border-color: #000000 !important;
}

/* 强制覆盖所有可能的popover样式 */
body .el-popover.menu-remark-popover {
  background-color: #000000 !important;
  color: #ffffff !important;
}

body .el-popover.menu-remark-popover .menu-remark-content {
  background-color: #000000 !important;
  color: #ffffff !important;
}

/* 控制按钮样式 */
.remark-tooltip-btn {
  margin-right: 12px;
  padding: 8px;
}
</style>
