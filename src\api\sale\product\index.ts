// src/api/sale/product/index.ts
// 商品模块统一导出

// 商品分类相关
export * from './category'

// 商品相关
export {
  type PageResult,
  type Product,
  type ProductAddDTO,
  type ProductUpdateDTO,
  type ProductPageQuery,
  type ProductSortDTO,
  type ProductExportQuery,
  getProductPage,
  getProductById,
  addProduct,
  updateProduct,
  deleteProduct,
  batchUpdateProductSort,
  exportProduct,
} from './product'

// 商品规格相关
export {
  type ProductSpec,
  type ProductSpecAddDTO,
  type ProductSpecUpdateDTO,
  type ProductSpecSortDTO,
  getSpecsByProductId,
  getSpecById,
  addSpec,
  updateSpec,
  deleteSpec,
  batchUpdateSpecSort,
} from './spec'
