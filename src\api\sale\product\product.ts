// src/api/sale/product.ts
import request from '../../request'
import type { ApiResult } from './category'
import type { ProductSpecAddDTO } from './spec'

// 分页结果类型
export interface PageResult<T> {
  total: number
  rows: T[]
}

// 商品实体类型
export interface Product {
  id: number
  name: string
  categoryId: number
  sort: number
}

// 商品新增DTO
export interface ProductAddDTO {
  name: string
  categoryId: number
  specs?: ProductSpecAddDTO[]
}

// 商品更新DTO
export interface ProductUpdateDTO {
  id: number
  name: string
  categoryId: number
  sort?: number
}

// 商品查询参数
export interface ProductPageQuery {
  page: number
  size: number
  name?: string
  categoryId?: number
}

// 分页查询商品
export const getProductPage = (
  params: ProductPageQuery,
): Promise<ApiResult<PageResult<Product>>> => {
  return request({
    url: '/sale/product/page',
    method: 'get',
    params,
  })
}

// 根据ID获取商品
export const getProductById = (id: number): Promise<ApiResult<Product>> => {
  return request({
    url: `/sale/product/${id}`,
    method: 'get',
  })
}

// 新增商品
export const addProduct = (data: ProductAddDTO): Promise<ApiResult<string>> => {
  return request({
    url: '/sale/product',
    method: 'post',
    data,
  })
}

// 更新商品
export const updateProduct = (data: ProductUpdateDTO): Promise<ApiResult<string>> => {
  return request({
    url: '/sale/product',
    method: 'put',
    data,
  })
}

// 删除商品
export const deleteProduct = (id: number): Promise<ApiResult<string>> => {
  return request({
    url: `/sale/product/${id}`,
    method: 'delete',
  })
}

// 商品排序DTO
export interface ProductSortDTO {
  id: number
  sort: number
}

// 批量更新商品排序
export const batchUpdateProductSort = (data: ProductSortDTO[]): Promise<ApiResult<string>> => {
  return request({
    url: '/sale/product/batch-update-sort',
    method: 'put',
    data,
  })
}

// 商品导出查询参数
export interface ProductExportQuery {
  id?: number
  name?: string
  categoryId?: number
  isSpec?: boolean
}

// 导出商品数据
export const exportProduct = (params: ProductExportQuery): Promise<Blob> => {
  return request({
    url: '/sale/product/export',
    method: 'get',
    params,
    responseType: 'blob',
  }).then((response) => {
    // 如果request返回的是完整的response对象，取data
    // 如果已经是blob，直接返回
    return response.data || response
  })
}
