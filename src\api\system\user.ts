// src/api/user.ts
import request from '../request'

// 定义接口类型
export interface LoginDTO {
  username: string
  password: string
}

export interface LoginVO {
  userId: number
  username: string
  position: string
  token: string
}

export interface User {
  id: number
  username: string
  password: string
  position: string
  status: string
  sortOrder: number
}

export interface ApiResult<T> {
  code: number
  msg: string
  data: T
}

// 用户登录
export const login = (data: LoginDTO): Promise<ApiResult<LoginVO>> => {
  return request({
    url: '/auth/login',
    method: 'post',
    data,
  })
}

// 获取当前用户信息
export const getCurrentUser = (): Promise<ApiResult<User>> => {
  return request({
    url: '/auth/current',
    method: 'get',
  })
}

// 退出登录
export const logout = (): Promise<ApiResult<string>> => {
  return request({
    url: '/auth/logout',
    method: 'post',
  })
}

// 菜单相关接口类型
export interface SysMenu {
  id: number
  parentId: number
  menuName: string
  icon: string
  path: string
  component: string
  sortOrder: number
  remark: string
  children: SysMenu[]
}

export interface MenuAddDTO {
  parentId: number
  menuName: string
  icon?: string
  path?: string
  component?: string
  sortOrder: number
  remark?: string
}

export interface MenuUpdateDTO {
  id: number
  parentId: number
  menuName: string
  icon?: string
  path?: string
  component?: string
  sortOrder: number
  remark?: string
}

export interface UserMenuAssignDTO {
  userId: number
  menuIds: number[]
}

// 获取菜单树
export const getMenuTree = (): Promise<ApiResult<SysMenu[]>> => {
  return request({
    url: '/system/menu/tree',
    method: 'get',
  })
}

// 获取当前用户菜单树
export const getUserMenuTree = (): Promise<ApiResult<SysMenu[]>> => {
  return request({
    url: '/system/menu/user-tree',
    method: 'get',
  })
}

// 根据ID获取菜单
export const getMenuById = (id: number): Promise<ApiResult<SysMenu>> => {
  return request({
    url: `/system/menu/${id}`,
    method: 'get',
  })
}

// 新增菜单
export const addMenu = (data: MenuAddDTO): Promise<ApiResult<string>> => {
  return request({
    url: '/system/menu',
    method: 'post',
    data,
  })
}

// 更新菜单
export const updateMenu = (data: MenuUpdateDTO): Promise<ApiResult<string>> => {
  return request({
    url: '/system/menu',
    method: 'put',
    data,
  })
}

// 删除菜单
export const deleteMenu = (id: number): Promise<ApiResult<string>> => {
  return request({
    url: `/system/menu/${id}`,
    method: 'delete',
  })
}

// 批量更新菜单排序
/**
 * 批量更新菜单排序值
 * @param data 需要更新的菜单项数组，包含id和sortOrder
 * @returns Promise<ApiResult<string>>
 */
export const batchUpdateMenuSort = (data: Array<{ id: number; sortOrder: number }>) => {
  return request({
    url: '/system/menu/batch-update-sort',
    method: 'put',
    data,
  })
}

// 为用户分配菜单权限
export const assignUserMenus = (data: UserMenuAssignDTO): Promise<ApiResult<string>> => {
  return request({
    url: '/system/menu/assign',
    method: 'post',
    data,
  })
}

// 获取用户已分配的菜单ID列表
export const getUserMenuIds = (userId: number): Promise<ApiResult<number[]>> => {
  return request({
    url: `/system/menu/user-menus/${userId}`,
    method: 'get',
  })
}

// 检查用户是否有菜单权限
export const checkMenuPermission = (
  userId: number,
  menuId: number,
): Promise<ApiResult<boolean>> => {
  return request({
    url: '/system/menu/check-permission',
    method: 'get',
    params: { userId, menuId },
  })
}

// ===========================
// 用户管理API接口
// ===========================

export interface UserAddDTO {
  username: string
  password: string
  position?: string
  status?: string
}

export interface UserUpdateDTO {
  id: number
  username: string
  position?: string
  status?: string
}

// 用户排序更新DTO
export interface UserSortUpdateDTO {
  id: number
  sortOrder: number
}

export interface PageResult<T> {
  total: number
  rows: T[]
}

export interface UserPageQuery {
  pageNum?: number
  pageSize?: number
  username?: string
  position?: string
  status?: string
  sortField?: string
  sortOrder?: string
}

// 分页查询用户
export const getUserPage = (params: UserPageQuery): Promise<ApiResult<PageResult<User>>> => {
  return request({
    url: '/system/user/page',
    method: 'get',
    params,
  })
}

// 根据ID获取用户
export const getUserById = (id: number): Promise<ApiResult<User>> => {
  return request({
    url: `/system/user/${id}`,
    method: 'get',
  })
}

// 新增用户
export const addUser = (data: UserAddDTO): Promise<ApiResult<string>> => {
  return request({
    url: '/system/user',
    method: 'post',
    data,
  })
}

// 更新用户
export const updateUser = (data: UserUpdateDTO): Promise<ApiResult<string>> => {
  return request({
    url: '/system/user',
    method: 'put',
    data,
  })
}

// 删除用户
export const deleteUser = (id: number): Promise<ApiResult<string>> => {
  return request({
    url: `/system/user/${id}`,
    method: 'delete',
  })
}

// 批量删除用户
export const batchDeleteUsers = (ids: number[]): Promise<ApiResult<string>> => {
  return request({
    url: '/system/user/batch',
    method: 'delete',
    data: ids,
  })
}

// 导出用户数据
export const exportUsers = (params: UserPageQuery): Promise<Blob> => {
  return request({
    url: '/system/user/export',
    method: 'get',
    params,
    responseType: 'blob',
  }).then((response) => {
    // 如果request返回的是完整的response对象，取data
    // 如果已经是blob，直接返回
    return response.data || response
  })
}

// 下载导入模板
export const downloadTemplate = (): Promise<Blob> => {
  return request({
    url: '/system/user/template',
    method: 'get',
    responseType: 'blob',
  }).then((response) => {
    // 如果request返回的是完整的response对象，取data
    // 如果已经是blob，直接返回
    return response.data || response
  })
}

// 导入用户数据
export const importUsers = (file: File): Promise<ApiResult<string>> => {
  const formData = new FormData()
  formData.append('file', file)
  return request({
    url: '/system/user/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}

// 批量更新用户排序
export const updateUserSortOrder = (data: UserSortUpdateDTO[]): Promise<ApiResult<string>> => {
  return request({
    url: '/system/user/sort-order',
    method: 'put',
    data,
  })
}
