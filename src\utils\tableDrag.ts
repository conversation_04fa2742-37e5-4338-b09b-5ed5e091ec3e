/**
 * 普通表格拖拽工具函数
 * 专门用于处理普通表格的拖拽排序功能
 */

import Sortable from 'sortablejs'
import type { SortableEvent } from 'sortablejs'

// 拖拽配置接口
export interface DragConfig {
  animation?: number
  ghostClass?: string
  chosenClass?: string
  dragClass?: string
  handle?: string
}

/**
 * 初始化普通表格拖拽功能
 * @param tableSelector 表格选择器字符串或DOM元素
 * @param onDragEnd 拖拽结束回调
 * @param config 拖拽配置
 * @returns Sortable实例
 */
export const inittabledrag = (
  tableSelector: string | HTMLElement,
  onDragEnd: (oldIndex: number, newIndex: number) => void,
  config: DragConfig = {},
): Sortable => {
  let tbody: HTMLElement | null

  try {
    if (typeof tableSelector === 'string') {
      const table = document.querySelector(tableSelector) as HTMLElement | null
      if (!table) {
        throw new Error(`未找到表格元素: ${tableSelector}`)
      }
      // 对于element plus表格，需要查找特定的tbody结构
      tbody = table.querySelector('.el-table__body-wrapper tbody')
    } else {
      // 检查传入的元素是否已经是tbody
      if (tableSelector.tagName === 'TBODY') {
        tbody = tableSelector
      } else {
        // 对于element plus表格，需要查找特定的tbody结构
        tbody = tableSelector.querySelector('.el-table__body-wrapper tbody') as HTMLElement | null
      }
    }

    if (!tbody) {
      throw new Error('未找到表格tbody元素')
    }

    // 检查tbody是否有子元素
    if (tbody.children.length === 0) {
      console.warn('表格tbody没有子元素，拖拽功能可能无法正常工作')
    }

    const defaultConfig = {
      animation: 200,
      ghostClass: 'sortable-ghost',
      chosenClass: 'sortable-chosen',
      dragClass: 'sortable-drag',
      ...config,
    }

    // 创建sortable实例，添加错误处理
    const sortableInstance = new Sortable(tbody, {
      animation: defaultConfig.animation,
      ghostClass: defaultConfig.ghostClass,
      chosenClass: defaultConfig.chosenClass,
      dragClass: defaultConfig.dragClass,

      onEnd: (evt: SortableEvent) => {
        try {
          const { oldIndex, newIndex } = evt
          if (oldIndex !== undefined && newIndex !== undefined) {
            onDragEnd(oldIndex, newIndex)
          }
        } catch (error) {
          console.error('拖拽结束回调失败:', error)
        }
      },

      // 添加错误处理
      onStart: () => {
        try {
          // 拖拽开始时的处理
        } catch (error) {
          console.error('拖拽开始失败:', error)
        }
      },
    })

    return sortableInstance
  } catch (error) {
    console.error('初始化拖拽功能失败:', error)
    throw error
  }
}

/**
 * 安全销毁拖拽实例
 * @param sortable Sortable实例
 */
export const destroytabledrag = (sortable: Sortable | null): void => {
  try {
    if (sortable) {
      // 检查sortable实例是否还有效
      if (typeof sortable.destroy === 'function') {
        sortable.destroy()
        console.log('拖拽实例已成功销毁')
      } else {
        console.warn('sortable实例的destroy方法不可用')
      }
    }
  } catch (error) {
    console.error('销毁拖拽实例时发生错误:', error)
    // 即使销毁失败，也要将引用置为null，避免内存泄漏
  }
}
