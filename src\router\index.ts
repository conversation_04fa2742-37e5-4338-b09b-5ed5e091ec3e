import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/counter'
import { usePermissionStore } from '@/stores/permission'
import Login from '@/views/auth/Login.vue'
import MainLayout from '@/layout/MainLayout.vue'
import Dashboard from '@/views/Dashboard.vue'
import Profile from '@/views/Profile.vue'
import AddProduct from '@/views/sale/product/AddProduct.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/dashboard',
    },
    {
      path: '/login',
      name: 'login',
      component: Login,
      meta: { requiresAuth: false },
    },
    {
      path: '/',
      name: 'main',
      component: MainLayout,
      meta: { requiresAuth: true },
      children: [
        {
          path: 'dashboard',
          name: 'dashboard',
          component: Dashboard,
          meta: { title: '首页' },
        },
        {
          path: 'profile',
          name: 'profile',
          component: Profile,
          meta: { title: '个人中心' },
        },
        {
          path: 'sale/product/add',
          name: 'add-product',
          component: AddProduct,
          meta: { title: '添加商品' },
        },
      ],
    },
  ],
})

// 路由守卫
router.beforeEach(async (to, _from, next) => {
  const userStore = useUserStore()
  const permissionStore = usePermissionStore()

  // 检查路由是否需要认证
  if (to.meta.requiresAuth !== false) {
    // 需要认证的路由
    if (!userStore.isLoggedIn) {
      // 未登录，跳转到登录页
      next('/login')
      return
    }

    // 已登录，检查是否已生成动态路由
    if (!permissionStore.isRoutesGenerated) {
      try {
        // 初始化权限数据并生成动态路由
        const success = await permissionStore.initPermission()
        if (success) {
          // 动态添加路由
          const dynamicRoutes = permissionStore.dynamicRoutes
          dynamicRoutes.forEach((route) => {
            router.addRoute('main', route) // 添加到主布局下
          })

          // 重新导航到目标路由
          next({ ...to, replace: true })
          return
        } else {
          // 获取权限失败，跳转到登录页
          next('/login')
          return
        }
      } catch (error) {
        console.error('初始化权限失败:', error)
        next('/login')
        return
      }
    }

    // 检查路径权限（对于动态路由）
    if (to.meta.menuId && !permissionStore.hasMenuPermission(to.meta.menuId as number)) {
      // 没有权限，跳转到403页面或首页
      next('/dashboard')
      return
    }
  } else {
    // 不需要认证的路由（如登录页）
    if (userStore.isLoggedIn && to.path === '/login') {
      // 已登录用户访问登录页，跳转到首页
      next('/dashboard')
      return
    }
  }

  next()
})

export default router
