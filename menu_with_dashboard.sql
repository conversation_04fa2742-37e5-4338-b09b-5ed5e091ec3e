-- 更新菜单表数据，添加首页菜单
-- 清空现有数据
DELETE FROM `sys_user_menu`;
DELETE FROM `sys_menus`;

-- 重置自增ID
ALTER TABLE `sys_menus` AUTO_INCREMENT = 1;

-- ===========================
-- 菜单表数据（包含首页）
-- ===========================
INSERT INTO `sys_menus` (`id`, `parent_id`, `menu_name`, `icon`, `path`, `component`, `sort_order`) VALUES
-- 首页（独立菜单）
(1, 0, '首页', 'House', '/dashboard', 'Dashboard', 0),

-- 系统管理
(10, 0, '系统管理', 'Setting', '/system', '', 1),
(101, 10, '菜单管理', 'Menu', '/system/menu', 'MenuManagement', 1),
(102, 10, '用户管理', 'UserFilled', '/system/users', 'UserManagement', 2),
(103, 10, '系统设置', 'Tools', '/system/settings', 'SystemSettings', 3),
(104, 10, '操作日志', 'Document', '/system/logs', 'SystemLogs', 4),

-- 业务管理
(20, 0, '业务管理', 'Management', '/business', '', 2),
(201, 20, '订单管理', 'List', '/business/orders', 'OrderManagement', 1),
(202, 20, '客户管理', 'User', '/business/customers', 'CustomerManagement', 2),
(203, 20, '产品管理', 'Grid', '/business/products', 'ProductManagement', 3),
(204, 20, '库存管理', 'Files', '/business/inventory', 'InventoryManagement', 4),

-- 订单管理子菜单
(2011, 201, '订单列表', 'List', '/business/orders/list', 'OrderList', 1),
(2012, 201, '订单统计', 'DataAnalysis', '/business/orders/statistics', 'OrderStatistics', 2),

-- 报表中心
(30, 0, '报表中心', 'DataAnalysis', '/reports', '', 3),
(301, 30, '销售报表', 'DataAnalysis', '/reports/sales', 'SalesReport', 1),
(302, 30, '财务报表', 'Monitor', '/reports/finance', 'FinanceReport', 2),
(303, 30, '库存报表', 'Files', '/reports/inventory', 'InventoryReport', 3),

-- 个人中心（不显示在菜单中，但可以通过路由访问）
(40, 0, '个人中心', 'User', '/profile', 'Profile', 4);

-- ===========================
-- 用户菜单权限分配（包含首页）
-- ===========================

-- admin用户：拥有所有权限（包含首页）
INSERT INTO `sys_user_menu` (`user_id`, `menu_id`) VALUES
-- 首页权限
(1, 1),
-- 系统管理权限
(1, 10), (1, 101), (1, 102), (1, 103), (1, 104),
-- 业务管理权限
(1, 20), (1, 201), (1, 2011), (1, 2012), (1, 202), (1, 203), (1, 204),
-- 报表中心权限
(1, 30), (1, 301), (1, 302), (1, 303),
-- 个人中心权限
(1, 40);

-- manager用户：拥有业务管理和报表权限，部分系统管理权限（包含首页）
INSERT INTO `sys_user_menu` (`user_id`, `menu_id`) VALUES
-- 首页权限
(2, 1),
-- 部分系统管理权限（用户管理、操作日志）
(2, 10), (2, 102), (2, 104),
-- 完整业务管理权限
(2, 20), (2, 201), (2, 2011), (2, 2012), (2, 202), (2, 203), (2, 204),
-- 完整报表权限
(2, 30), (2, 301), (2, 302), (2, 303),
-- 个人中心权限
(2, 40);

-- user1用户：主要业务操作权限（包含首页）
INSERT INTO `sys_user_menu` (`user_id`, `menu_id`) VALUES
-- 首页权限
(3, 1),
-- 业务管理权限（订单、客户、产品）
(3, 20), (3, 201), (3, 2011), (3, 202), (3, 203),
-- 部分报表权限（销售报表）
(3, 30), (3, 301),
-- 个人中心权限
(3, 40);

-- user2用户：财务相关权限（包含首页）
INSERT INTO `sys_user_menu` (`user_id`, `menu_id`) VALUES
-- 首页权限
(4, 1),
-- 业务管理权限（订单统计、客户管理）
(4, 20), (4, 201), (4, 2012), (4, 202),
-- 报表权限（销售报表、财务报表）
(4, 30), (4, 301), (4, 302),
-- 个人中心权限
(4, 40);

-- guest用户：只有基本查看权限（包含首页）
INSERT INTO `sys_user_menu` (`user_id`, `menu_id`) VALUES
-- 首页权限
(5, 1),
-- 基本业务查看权限（订单列表、客户管理）
(5, 20), (5, 201), (5, 2011), (5, 202),
-- 个人中心权限
(5, 40);

-- ===========================
-- 验证数据查询
-- ===========================

-- 查看菜单树结构
SELECT 
    m1.id, m1.menu_name, m1.icon, m1.path, m1.component, m1.sort_order,
    m2.id as child_id, m2.menu_name as child_name, m2.path as child_path
FROM sys_menus m1 
LEFT JOIN sys_menus m2 ON m1.id = m2.parent_id 
WHERE m1.parent_id = 0 
ORDER BY m1.sort_order, m2.sort_order;

-- 查看admin用户的菜单权限
SELECT 
    u.username, u.position,
    m.menu_name, m.path, m.icon, m.component
FROM user u
JOIN sys_user_menu um ON u.id = um.user_id
JOIN sys_menus m ON um.menu_id = m.id
WHERE u.id = 1
ORDER BY m.sort_order;
