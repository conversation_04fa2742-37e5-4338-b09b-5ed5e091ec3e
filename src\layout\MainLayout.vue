<template>
  <div class="common-layout">
    <el-container class="layout-container">
      <!-- 侧边栏 -->
      <el-aside :width="isCollapse ? '64px' : '200px'" class="sidebar">
        <div class="logo">
          <h3 v-if="!isCollapse">企业管理系统</h3>
          <h3 v-else>ERP</h3>
        </div>
        <el-menu
          :default-active="activeMenu"
          class="sidebar-menu"
          :collapse="isCollapse"
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#409EFF"
          router
          :unique-opened="true"
        >
          <!-- 固定菜单项 -->
          <!-- <el-menu-item index="/dashboard">
            <el-icon><House /></el-icon>
            <template #title>首页</template>
          </el-menu-item> -->
          <!-- <el-menu-item index="/profile">
            <el-icon><User /></el-icon>
            <template #title>个人中心</template>
          </el-menu-item> -->

          <!-- 动态菜单项 -->
          <template v-for="menu in filteredMenus" :key="menu.id">
            <DynamicMenuItem :menu="menu" />
          </template>
        </el-menu>

        <!-- 侧边栏底部用户信息 -->
        <div class="sidebar-footer">
          <div class="user-info">
            <el-avatar :src="avatarUrl" :size="32">
              <el-icon><User /></el-icon>
            </el-avatar>
            <div class="user-details" v-if="!isCollapse">
              <div class="username">{{ userStore.userInfo?.username || '用户' }}</div>
              <div class="position">{{ userStore.userInfo?.position || '职位' }}</div>
            </div>
          </div>
          <div class="user-actions" v-if="!isCollapse">
            <el-dropdown @command="handleCommand">
              <el-button text>
                <el-icon><Setting /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">个人中心</el-dropdown-item>
                  <el-dropdown-item command="settings">系统设置</el-dropdown-item>
                  <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </el-aside>

      <el-container>
        <!-- 顶部导航 -->
        <el-header class="header">
          <div class="header-left">
            <el-button type="text" @click="toggleSidebar" class="collapse-btn">
              <el-icon><Expand v-if="isCollapse" /><Fold v-else /></el-icon>
            </el-button>
            <el-breadcrumb separator="/">
              <el-breadcrumb-item :to="{ path: '/dashboard' }">首页</el-breadcrumb-item>
              <el-breadcrumb-item v-if="currentRouteName">{{
                currentRouteName
              }}</el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          <div class="header-right">
            <!-- 菜单备注气泡框控制按钮 -->
            <el-tooltip content="菜单备注提示" placement="bottom" :hide-after="0">
              <el-button
                :type="permissionStore.showMenuRemarkTooltip ? 'success' : 'danger'"
                @click="toggleMenuRemarkTooltip"
                class="remark-tooltip-btn"
              >
                <el-icon><ChatDotRound /></el-icon>
              </el-button>
            </el-tooltip>
            <el-dropdown @command="handleCommand">
              <span class="user-info">
                <el-avatar :size="32" :src="avatarUrl">
                  <el-icon><User /></el-icon>
                </el-avatar>
                <span class="username">{{ userStore.userInfo?.username || '用户' }}</span>
                <el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">个人中心</el-dropdown-item>
                  <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>

        <!-- 标签页导航 -->
        <TabsView ref="tabsViewRef" />

        <!-- 主内容区 -->
        <el-main class="main-content">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores/counter'
import { usePermissionStore } from '@/stores/permission'
import type { SysMenu } from '@/api/system/user'
import {
  User,
  Setting,
  Expand,
  Fold,
  ArrowDown,
  ChatDotRound,
  House,
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import DynamicMenuItem from '@/components/DynamicMenuItem.vue'
import TabsView from '@/components/TabsView.vue'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
const permissionStore = usePermissionStore()

// 侧边栏折叠状态
const isCollapse = ref(false)

// 当前激活的菜单 - 改进高亮逻辑
const activeMenu = computed(() => {
  const currentPath = route.path

  // 直接匹配当前路径
  const findMatchingMenuPath = (menus: SysMenu[]): string | null => {
    for (const menu of menus) {
      // 如果当前路径完全匹配菜单路径
      if (menu.path === currentPath) {
        return menu.path
      }

      // 如果菜单有子菜单，递归查找
      if (menu.children && menu.children.length > 0) {
        const childMatch = findMatchingMenuPath(menu.children)
        if (childMatch) {
          return childMatch
        }
      }
    }
    return null
  }

  const matchedPath = findMatchingMenuPath(permissionStore.userMenus)
  return matchedPath || currentPath
})

// 当前路由名称 - 使用面包屑导航
const currentRouteName = computed(() => {
  const breadcrumb = permissionStore.getBreadcrumb(route.path)
  return breadcrumb.length > 0 ? breadcrumb[breadcrumb.length - 1].title : ''
})

// 过滤菜单（排除个人中心，保留首页和其他菜单）
const filteredMenus = computed(() => {
  // 排除个人中心，保留首页和其他所有菜单
  return permissionStore.userMenus.filter((menu) => menu.component !== 'Profile')
})

// 面包屑导航已替换为标签页

// 用户头像
const avatarUrl = ref('')

// 标签页组件引用
const tabsViewRef = ref()

// 切换侧边栏
const toggleSidebar = () => {
  isCollapse.value = !isCollapse.value
}

// 切换菜单备注气泡框显示状态
const toggleMenuRemarkTooltip = () => {
  permissionStore.setShowMenuRemarkTooltip(!permissionStore.showMenuRemarkTooltip)
}

// 处理下拉菜单命令
const handleCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      ElMessage.info('设置功能开发中...')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
        const success = await userStore.userLogout()
        if (success) {
          router.push('/login')
        }
      } catch {
        // 用户取消操作
      }
      break
  }
}

// 组件挂载时获取用户信息
onMounted(async () => {
  if (userStore.isLoggedIn && !userStore.userInfo) {
    await userStore.fetchCurrentUser()
  }
})
</script>

<style scoped>
.layout-container {
  height: 100vh;
  width: 100vw;
}

.sidebar {
  background-color: #304156;
  transition: width 0.3s;
  overflow: hidden;
}

.logo {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #2b3a4b;
  color: #fff;
  font-size: 18px;
  font-weight: bold;
}

.sidebar-menu {
  border: none;
  height: calc(100vh - 60px);
  overflow-y: auto;
}

.sidebar-menu:not(.el-menu--collapse) {
  width: 200px;
}

.header {
  background-color: #fff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 30px;
  padding: 0 20px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.collapse-btn {
  font-size: 18px;
  color: #606266;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 8px;
  height: 30px;
}

.remark-tooltip-btn {
  padding: 8px;
  min-width: auto;
  border: 1px solid transparent;
  background: transparent;
}

.remark-tooltip-btn:hover {
  background-color: rgba(64, 158, 255, 0.1);
}

/* 确保success类型显示绿色 */
.remark-tooltip-btn.el-button--success {
  color: var(--el-color-success);
  border-color: var(--el-color-success);
  background-color: rgba(103, 194, 58, 0.1);
}

.remark-tooltip-btn.el-button--success:hover {
  background-color: rgba(103, 194, 58, 0.2);
}

/* 确保danger类型显示红色 */
.remark-tooltip-btn.el-button--danger {
  color: var(--el-color-danger);
  border-color: var(--el-color-danger);
  background-color: rgba(245, 108, 108, 0.1);
}

.remark-tooltip-btn.el-button--danger:hover {
  background-color: rgba(245, 108, 108, 0.2);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f7fa;
}

.username {
  color: #606266;
  font-size: 14px;
}

.main-content {
  background-color: #f0f2f5;
  padding: 20px;
  overflow-y: auto;
  height: calc(100vh - 60px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .layout-container {
    flex-direction: column;
  }

  .sidebar {
    width: 100% !important;
    height: auto;
  }

  .logo {
    height: 50px;
    font-size: 16px;
  }

  .sidebar-menu {
    height: auto;
  }

  .header {
    padding: 0 10px;
  }

  .header-left {
    gap: 10px;
  }

  .main-content {
    padding: 10px;
    height: auto;
  }
}

@media (max-width: 480px) {
  .username {
    display: none;
  }

  .header {
    padding: 0 5px;
  }

  .main-content {
    padding: 5px;
  }
}
</style>
