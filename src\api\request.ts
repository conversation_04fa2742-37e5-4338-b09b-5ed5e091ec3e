// src/api/request.ts
import axios from 'axios'
import type { AxiosInstance, InternalAxiosRequestConfig, AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'

// 创建 axios 实例
const service: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_APP_API_BASE_URL || '/api',
  timeout: 5000,
  headers: {
    'Content-Type': 'application/json;charset=utf-8',
  },
})

// 请求拦截器
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 获取 token 并添加到请求头
    const token = localStorage.getItem('token')
    if (token) {
      config.headers = config.headers || {}
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  },
)

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse) => {
    const { data, status, config } = response

    // 处理文件下载请求（blob类型）
    if (config.responseType === 'blob' || config.responseType === 'arraybuffer') {
      // 检查是否是错误响应（通常错误响应会是JSON格式）
      if (data instanceof Blob && data.type === 'application/json') {
        // 这是一个JSON错误响应，需要解析
        return data.text().then((text) => {
          try {
            const errorData = JSON.parse(text)
            ElMessage.error(errorData.msg || '请求失败')
            return Promise.reject(errorData)
          } catch (e) {
            ElMessage.error('请求失败')
            return Promise.reject(new Error('请求失败'))
          }
        })
      }
      // 正常的blob响应，直接返回
      return response
    }

    // 处理普通JSON响应

    if (status === 200) {
      // 根据实际后端返回，成功时code为1，失败时为其他数字
      if (data.code === 1) {
        return data // 返回完整的响应数据，包含code、msg、data
      } else {
        console.error('接口错误:', data.msg)
        ElMessage.error(data.msg || '请求失败')
        return Promise.reject(new Error(data.msg || '请求失败'))
      }
    } else {
      console.error('请求失败:', status)
      ElMessage.error(`请求失败，状态码: ${status}`)
      return Promise.reject(new Error(`请求失败，状态码: ${status}`))
    }
  },
  (error) => {
    console.error('响应错误:', error)
    // 统一处理错误消息
    const errorMessage = error.response?.data?.msg || error.message || '未知错误'
    ElMessage.error(errorMessage) // 处理 token 过期等特殊情况

    if (error.response && error.response.status === 401) {
      // 清除本地存储的用户信息
      localStorage.removeItem('token')
      localStorage.removeItem('userInfo') // 跳转到登录页
      window.location.href = '/login'
    }
    return Promise.reject(error)
  },
)

export default service
