<template>
  <div class="profile-container">
    <el-card class="profile-card">
      <template #header>
        <div class="card-header">
          <span>个人信息</span>
          <el-button type="primary" @click="editMode = !editMode">
            {{ editMode ? '取消编辑' : '编辑信息' }}
          </el-button>
        </div>
      </template>

      <div class="profile-content">
        <!-- 头像区域 -->
        <div class="avatar-section">
          <el-avatar :size="120" :src="avatarUrl" class="user-avatar">
            <el-icon size="60"><User /></el-icon>
          </el-avatar>
          <div class="avatar-actions" v-if="editMode">
            <el-button type="text" @click="changeAvatar">更换头像</el-button>
          </div>
        </div>

        <!-- 信息表单 -->
        <div class="info-section">
          <el-form
            ref="profileForm"
            :model="formData"
            :rules="rules"
            label-width="100px"
            class="profile-form"
          >
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="用户ID" prop="id">
                  <el-input v-model="formData.id" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="用户名" prop="username">
                  <el-input 
                    v-model="formData.username" 
                    :disabled="!editMode"
                    placeholder="请输入用户名"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="岗位" prop="position">
                  <el-input 
                    v-model="formData.position" 
                    :disabled="!editMode"
                    placeholder="请输入岗位"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="状态" prop="status">
                  <el-select 
                    v-model="formData.status" 
                    :disabled="!editMode"
                    placeholder="请选择状态"
                    style="width: 100%"
                  >
                    <el-option label="正常" value="active" />
                    <el-option label="禁用" value="inactive" />
                    <el-option label="锁定" value="locked" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" v-if="editMode">
              <el-col :span="12">
                <el-form-item label="新密码" prop="newPassword">
                  <el-input 
                    v-model="formData.newPassword" 
                    type="password"
                    placeholder="留空则不修改密码"
                    show-password
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="确认密码" prop="confirmPassword">
                  <el-input 
                    v-model="formData.confirmPassword" 
                    type="password"
                    placeholder="请再次输入新密码"
                    show-password
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item v-if="editMode" class="form-actions">
              <el-button type="primary" @click="saveProfile" :loading="saving">
                保存修改
              </el-button>
              <el-button @click="resetForm">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </el-card>

    <!-- 操作记录卡片 -->
    <el-card class="activity-card">
      <template #header>
        <span>最近活动</span>
      </template>
      <el-timeline>
        <el-timeline-item
          v-for="activity in activities"
          :key="activity.id"
          :timestamp="activity.timestamp"
          :type="activity.type"
        >
          {{ activity.content }}
        </el-timeline-item>
      </el-timeline>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { useUserStore } from '@/stores/counter'
import { User } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'

const userStore = useUserStore()

// 编辑模式
const editMode = ref(false)
const saving = ref(false)

// 头像URL
const avatarUrl = ref('')

// 表单数据
const formData = reactive({
  id: '',
  username: '',
  position: '',
  status: '',
  newPassword: '',
  confirmPassword: ''
})

// 表单验证规则
const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 20, message: '用户名长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  position: [
    { required: true, message: '请输入岗位', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ],
  newPassword: [
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    {
      validator: (rule: any, value: string, callback: Function) => {
        if (formData.newPassword && value !== formData.newPassword) {
          callback(new Error('两次输入密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 活动记录
const activities = ref([
  {
    id: 1,
    content: '登录系统',
    timestamp: new Date().toLocaleString(),
    type: 'primary'
  },
  {
    id: 2,
    content: '查看个人信息',
    timestamp: new Date(Date.now() - 1000 * 60 * 30).toLocaleString(),
    type: 'success'
  },
  {
    id: 3,
    content: '修改个人信息',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toLocaleString(),
    type: 'warning'
  }
])

const profileForm = ref<FormInstance>()

// 初始化表单数据
const initFormData = () => {
  if (userStore.userInfo) {
    formData.id = userStore.userInfo.id.toString()
    formData.username = userStore.userInfo.username
    formData.position = userStore.userInfo.position
    formData.status = userStore.userInfo.status
  }
}

// 重置表单
const resetForm = () => {
  initFormData()
  formData.newPassword = ''
  formData.confirmPassword = ''
  profileForm.value?.clearValidate()
}

// 保存个人信息
const saveProfile = async () => {
  if (!profileForm.value) return
  
  try {
    const valid = await profileForm.value.validate()
    if (!valid) return

    saving.value = true
    
    // 这里应该调用更新用户信息的API
    // 由于OpenAPI中没有更新用户信息的接口，这里模拟保存
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 更新本地用户信息
    if (userStore.userInfo) {
      userStore.userInfo.username = formData.username
      userStore.userInfo.position = formData.position
      userStore.userInfo.status = formData.status
      localStorage.setItem('userInfo', JSON.stringify(userStore.userInfo))
    }
    
    ElMessage.success('保存成功')
    editMode.value = false
    
    // 添加活动记录
    activities.value.unshift({
      id: Date.now(),
      content: '修改个人信息',
      timestamp: new Date().toLocaleString(),
      type: 'success'
    })
    
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

// 更换头像
const changeAvatar = () => {
  ElMessage.info('头像上传功能开发中...')
}

// 监听用户信息变化
watch(() => userStore.userInfo, (newUserInfo) => {
  if (newUserInfo) {
    initFormData()
  }
}, { immediate: true })

// 组件挂载时获取用户信息
onMounted(async () => {
  if (userStore.isLoggedIn && !userStore.userInfo) {
    await userStore.fetchCurrentUser()
  }
  initFormData()
})
</script>

<style scoped>
.profile-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.profile-card {
  flex: 1;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.profile-content {
  display: flex;
  gap: 40px;
  align-items: flex-start;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  min-width: 160px;
}

.user-avatar {
  border: 3px solid #e4e7ed;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.avatar-actions {
  text-align: center;
}

.info-section {
  flex: 1;
}

.profile-form {
  max-width: 800px;
}

.form-actions {
  margin-top: 30px;
  text-align: center;
}

.activity-card {
  max-height: 400px;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .profile-content {
    flex-direction: column;
    align-items: center;
    gap: 20px;
  }
  
  .avatar-section {
    min-width: auto;
  }
  
  .info-section {
    width: 100%;
  }
  
  .profile-form {
    max-width: 100%;
  }
}

@media (max-width: 480px) {
  .profile-container {
    padding: 0 10px;
  }
  
  .card-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
  
  .user-avatar {
    width: 80px !important;
    height: 80px !important;
  }
}
</style>
