/* 拖拽样式 */
/* use context7: 表格拖拽样式实现 */

/* sortablejs 拖拽时的样式 */
.sortable-ghost {
  opacity: 0.4 !important;
  background-color: #f5f7fa !important;
}

.sortable-chosen {
  background-color: #ecf5ff !important;
}

.sortable-drag {
  background-color: #409eff !important;
  color: white !important;
  opacity: 0.8 !important;
}

/* element plus 表格行拖拽样式 */
.el-table__row {
  cursor: move;
  transition: background-color 0.2s ease;
}

.el-table__row:hover {
  background-color: #f5f7fa !important;
}

/* HTML5 拖拽状态样式 */
.el-table__row[draggable="true"] {
  cursor: move;
}

/* 拖拽中的行样式 */
.el-table__row.dragging {
  background-color: #409eff !important;
  color: white !important;
  opacity: 0.8 !important;
}

/* 拖拽悬停目标行样式 */
.el-table__row.drag-over {
  background-color: #e6f7ff !important;
}

/* 拖拽悬停目标行上方样式 */
.el-table__row.drag-over-before {
  border-top: 2px solid #409eff !important;
}

/* 拖拽悬停目标行下方样式 */
.el-table__row.drag-over-after {
  border-bottom: 2px solid #409eff !important;
}

/* 确保拖拽时文字颜色正确 */
.el-table__row.dragging .cell {
  color: white !important;
}

/* 拖拽时的表格体样式 */
.el-table__body-wrapper {
  user-select: none;
}

/* 拖拽时的占位符样式 */
.el-table__row.drag-over .cell {
  opacity: 0.7;
}

/* sortablejs 兼容样式（保留以备后用） */
.el-table__row.sortable-ghost {
  background-color: #e6f7ff !important;
}

.el-table__row.sortable-chosen {
  background-color: #409eff !important;
  color: white !important;
}

.el-table__row.sortable-drag .cell {
  color: white !important;
}

.el-table__row.sortable-ghost .cell {
  opacity: 0.4;
}

/* use context7: 树形表格箭头对齐样式 */
/* 树形表格展开/折叠箭头与文字对齐优化 */
.el-table__row .el-table__expand-icon {
  display: inline-flex !important;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
  margin-right: 4px;
  transform: translateY(0);
  cursor: pointer !important;
  position: static !important;
  z-index: auto !important;
}

/* 确保箭头图标与文字基线对齐 */
.el-table__row .el-table__expand-icon .el-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

/* 优化树形表格单元格内容对齐 - 但保持箭头可点击 */
.el-table__row .cell {
  display: flex;
  align-items: center;
  vertical-align: middle;
  line-height: 1.5;
}

/* 特殊处理菜单名称列的对齐 */
.el-table__row .menu-name-cell {
  display: flex;
  align-items: center;
  gap: 6px;
  vertical-align: middle;
  line-height: 1.5;
}

/* 确保图标与文字对齐 */
.el-table__row .menu-name-cell .el-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  line-height: 1;
}

/* 优化树形表格的缩进和对齐 */
.el-table__row.el-table__row--level-1 .cell {
  padding-left: 24px;
}

.el-table__row.el-table__row--level-2 .cell {
  padding-left: 48px;
}

.el-table__row.el-table__row--level-3 .cell {
  padding-left: 72px;
}

/* 鼠标悬停时箭头样式优化 */
.el-table__row:hover .el-table__expand-icon {
  background-color: rgba(64, 158, 255, 0.1);
  border-radius: 2px;
}

/* 确保箭头点击区域不被其他样式干扰 */
.el-table__row .el-table__expand-icon {
  pointer-events: auto !important;
}

/* 修正表格行的光标样式，箭头区域保持pointer，其他区域保持move */
.el-table__row {
  cursor: move;
}

.el-table__row .el-table__expand-icon {
  cursor: pointer !important;
}

/* use context7: 菜单备注气泡框黑色背景样式 */
/* 菜单备注气泡框 - 黑色背景白色字体 */
.el-popover.menu-remark-popover {
  background-color: #000000 !important;
  border: 1px solid #333333 !important;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3) !important;
  color: #ffffff !important;
}

.el-popover.menu-remark-popover .el-popper__arrow {
  border-color: #000000 !important;
}

.el-popover.menu-remark-popover .el-popper__arrow::before {
  background-color: #000000 !important;
  border-color: #000000 !important;
}

.el-popover.menu-remark-popover .menu-remark-content {
  background-color: #000000 !important;
  color: #ffffff !important;
}

.el-popover.menu-remark-popover .remark-text {
  color: #ffffff !important;
}