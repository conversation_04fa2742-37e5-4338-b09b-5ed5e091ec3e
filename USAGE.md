# 璟果ERP系统使用指南

## 系统概述

璟果ERP管理系统是一个基于Vue 3和Element Plus构建的现代化企业资源规划系统前端应用。系统提供了完整的用户认证、个人信息管理和仪表盘功能。

## 功能模块

### 1. 用户登录

**访问地址**: `/login`

**功能说明**:
- 用户名密码登录
- 自动记住登录状态
- 登录失败提示
- 响应式登录界面

**使用步骤**:
1. 在登录页面输入用户名和密码
2. 点击"登录"按钮
3. 系统验证成功后自动跳转到仪表盘

### 2. 仪表盘

**访问地址**: `/dashboard`

**功能说明**:
- 系统概览信息
- 统计数据展示
- 快捷操作入口
- 最近活动记录
- 系统通知

**主要组件**:
- **欢迎卡片**: 显示当前用户信息和日期
- **统计卡片**: 展示关键业务数据
- **快捷操作**: 常用功能的快速入口
- **活动时间线**: 显示最近的系统活动
- **通知中心**: 系统消息和提醒

### 3. 个人中心

**访问地址**: `/profile`

**功能说明**:
- 查看个人信息
- 编辑个人资料
- 修改密码
- 查看活动记录

**可编辑字段**:
- 用户名
- 岗位信息
- 账户状态
- 登录密码

**使用步骤**:
1. 点击右上角用户头像下拉菜单
2. 选择"个人中心"
3. 点击"编辑信息"按钮进入编辑模式
4. 修改相关信息后点击"保存修改"

## 系统布局

### 主布局结构

系统采用经典的左侧边栏 + 顶部导航 + 主内容区的布局：

```
┌─────────────────────────────────────┐
│              顶部导航栏              │
├──────────┬──────────────────────────┤
│          │                          │
│  侧边栏  │       主内容区           │
│          │                          │
│          │                          │
└──────────┴──────────────────────────┘
```

### 侧边栏菜单

- **首页**: 系统仪表盘
- **个人中心**: 用户信息管理
- **系统管理**: 系统配置功能（开发中）

### 顶部导航

- **左侧**: 菜单折叠按钮、面包屑导航
- **右侧**: 用户信息下拉菜单

## 响应式设计

系统支持多种屏幕尺寸：

### 桌面端 (>768px)
- 完整的侧边栏显示
- 所有功能正常展示
- 最佳用户体验

### 平板端 (768px-480px)
- 侧边栏自适应调整
- 布局优化适配
- 保持功能完整性

### 移动端 (<480px)
- 移动优化布局
- 简化界面元素
- 触摸友好操作

## 数据存储

### 本地存储

系统使用浏览器本地存储保存以下信息：

- **token**: 用户认证令牌
- **userInfo**: 用户基本信息

### 状态管理

使用Pinia进行全局状态管理：

- **用户状态**: 登录状态、用户信息
- **应用状态**: 主题、语言等配置

## API接口

### 认证相关

- `POST /api/auth/login`: 用户登录
- `POST /api/auth/logout`: 用户登出  
- `GET /api/auth/current`: 获取当前用户信息

### 请求格式

**登录请求**:
```json
{
  "username": "用户名",
  "password": "密码"
}
```

**响应格式**:
```json
{
  "code": 0,
  "msg": "成功",
  "data": {
    "userId": 1,
    "username": "用户名",
    "position": "岗位",
    "token": "JWT令牌"
  }
}
```

## 常见问题

### Q: 登录后页面空白怎么办？
A: 检查浏览器控制台是否有错误信息，确认API服务是否正常运行。

### Q: 如何退出登录？
A: 点击右上角用户头像，选择"退出登录"。

### Q: 忘记密码怎么办？
A: 目前系统暂未提供密码重置功能，请联系系统管理员。

### Q: 系统支持哪些浏览器？
A: 支持Chrome 87+、Firefox 78+、Safari 14+、Edge 88+。

## 技术支持

如遇到技术问题，请联系开发团队或查看项目文档。

## 更新日志

### v1.0.0 (当前版本)
- 完成基础登录功能
- 实现个人中心模块
- 添加响应式布局
- 集成Element Plus组件库
