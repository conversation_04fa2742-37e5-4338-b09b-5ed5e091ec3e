/**
 * 文件下载工具函数
 */

/**
 * 通用文件下载函数
 * @param blob - Blob对象
 * @param filename - 文件名
 */
export const downloadFile = (blob: Blob, filename: string) => {
  // 检查是否是有效的Blob对象
  if (!(blob instanceof Blob)) {
    console.error('downloadFile: 参数不是有效的Blob对象', blob)
    throw new Error('无效的文件数据')
  }

  // 创建下载链接
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  
  // 添加到DOM并触发下载
  document.body.appendChild(link)
  link.click()
  
  // 清理
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
}

/**
 * 从响应头获取文件名
 * @param response - axios响应对象
 * @param defaultName - 默认文件名
 */
export const getFilenameFromResponse = (response: any, defaultName: string): string => {
  try {
    const contentDisposition = response.headers['content-disposition']
    if (contentDisposition) {
      // 尝试匹配 filename*=utf-8''filename 格式
      let match = contentDisposition.match(/filename\*=utf-8''([^;]+)/)
      if (match && match[1]) {
        return decodeURIComponent(match[1])
      }
      
      // 尝试匹配 filename="filename" 格式
      match = contentDisposition.match(/filename="([^"]+)"/)
      if (match && match[1]) {
        return match[1]
      }
      
      // 尝试匹配 filename=filename 格式
      match = contentDisposition.match(/filename=([^;]+)/)
      if (match && match[1]) {
        return match[1].trim()
      }
    }
  } catch (error) {
    console.warn('解析文件名失败:', error)
  }
  
  return defaultName
}

/**
 * 处理文件下载错误
 * @param error - 错误对象
 * @param defaultMessage - 默认错误消息
 */
export const handleDownloadError = async (error: any, defaultMessage: string): Promise<string> => {
  // 如果错误是Blob类型，可能是后端返回的JSON错误信息
  if (error instanceof Blob) {
    try {
      const text = await error.text()
      const errorData = JSON.parse(text)
      return errorData.msg || defaultMessage
    } catch (e) {
      console.warn('解析错误信息失败:', e)
      return defaultMessage
    }
  }
  
  // 如果是axios错误
  if (error.response?.data?.msg) {
    return error.response.data.msg
  }
  
  // 如果是普通错误对象
  if (error.message) {
    return error.message
  }
  
  return defaultMessage
}

/**
 * 验证文件类型
 * @param blob - Blob对象
 * @param expectedTypes - 期望的MIME类型数组
 */
export const validateFileType = (blob: Blob, expectedTypes: string[]): boolean => {
  if (!(blob instanceof Blob)) {
    return false
  }
  
  // 如果没有指定期望类型，则认为有效
  if (!expectedTypes || expectedTypes.length === 0) {
    return true
  }
  
  return expectedTypes.some(type => blob.type.includes(type))
}

/**
 * Excel文件下载专用函数
 * @param blob - Blob对象
 * @param filename - 文件名（不含扩展名）
 */
export const downloadExcelFile = (blob: Blob, filename: string) => {
  // 验证是否是Excel文件类型
  const excelTypes = [
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-excel',
    'application/octet-stream'
  ]
  
  if (!validateFileType(blob, excelTypes)) {
    console.warn('文件类型可能不是Excel格式:', blob.type)
  }
  
  // 确保文件名有正确的扩展名
  const finalFilename = filename.endsWith('.xlsx') ? filename : `${filename}.xlsx`
  
  downloadFile(blob, finalFilename)
}
