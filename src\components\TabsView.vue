<template>
  <div class="tabs-view">
    <el-tabs
      v-model="activeTab"
      type="card"
      closable
      @tab-remove="removeTab"
      @tab-click="handleTabClick"
    >
      <el-tab-pane v-for="tab in tabs" :key="tab.path" :label="tab.title" :name="tab.path">
        <template #label>
          <span class="tab-label">
            <el-icon v-if="tab.icon" class="tab-icon">
              <component :is="getIconComponent(tab.icon)" />
            </el-icon>
            {{ tab.title }}
          </span>
        </template>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

interface Tab {
  path: string
  title: string
  icon?: string
  closable?: boolean
}

const route = useRoute()
const router = useRouter()

// 从localStorage恢复标签页状态
const loadTabsFromStorage = (): Tab[] => {
  try {
    const stored = localStorage.getItem('app_tabs')
    if (stored) {
      const parsedTabs = JSON.parse(stored)
      // 确保首页始终存在
      const hasHome = parsedTabs.some((tab: Tab) => tab.path === '/dashboard')
      if (!hasHome) {
        return [{
          path: '/dashboard',
          title: '首页',
          icon: 'House',
          closable: false,
        }, ...parsedTabs]
      }
      return parsedTabs
    }
  } catch (error) {
    console.error('恢复标签页状态失败:', error)
  }
  // 默认标签页
  return [{
    path: '/dashboard',
    title: '首页',
    icon: 'House',
    closable: false, // 首页不可关闭
  }]
}

// 保存标签页状态到localStorage
const saveTabsToStorage = (tabs: Tab[]) => {
  try {
    localStorage.setItem('app_tabs', JSON.stringify(tabs))
  } catch (error) {
    console.error('保存标签页状态失败:', error)
  }
}

// 标签页数据
const tabs = ref<Tab[]>(loadTabsFromStorage())

// 当前激活的标签页
const activeTab = ref('/dashboard')

// 获取图标组件 - 支持所有Element Plus图标
const getIconComponent = (iconName: string) => {
  // 直接从ElementPlusIconsVue中获取图标组件
  const iconComponent = (ElementPlusIconsVue as any)[iconName]

  // 如果找不到图标，返回默认的Setting图标
  return iconComponent || ElementPlusIconsVue.Setting
}

// 添加标签页
const addTab = (path: string, title: string, icon?: string) => {
  const existingTab = tabs.value.find((tab) => tab.path === path)
  if (!existingTab) {
    const newTab = {
      path,
      title,
      icon,
      closable: path !== '/dashboard', // 首页不可关闭
    }
    tabs.value.push(newTab)
    saveTabsToStorage(tabs.value)
  }
  activeTab.value = path
}

// 移除标签页
const removeTab = (targetPath: string) => {
  const targetIndex = tabs.value.findIndex((tab) => tab.path === targetPath)
  if (targetIndex === -1) return

  // 不能关闭首页
  if (targetPath === '/dashboard') return

  // 如果关闭的是当前激活的标签页，需要切换到其他标签页
  if (activeTab.value === targetPath) {
    const nextTab = tabs.value[targetIndex + 1] || tabs.value[targetIndex - 1]
    if (nextTab) {
      activeTab.value = nextTab.path
      router.push(nextTab.path)
    }
  }

  tabs.value.splice(targetIndex, 1)
  saveTabsToStorage(tabs.value)
}

// 点击标签页
const handleTabClick = (tab: any) => {
  const targetPath = tab.paneName
  if (targetPath !== route.path) {
    router.push(targetPath)
  }
}

// 监听路由变化，自动添加标签页
watch(
  () => route.path,
  (newPath) => {
    if (newPath && route.meta?.title) {
      addTab(newPath, route.meta.title as string, route.meta.icon as string)
    }
    activeTab.value = newPath
  },
  { immediate: true },
)

// 监听标签页变化，保存到localStorage
watch(
  () => tabs.value,
  (newTabs) => {
    saveTabsToStorage(newTabs)
  },
  { deep: true }
)

// 组件挂载时恢复激活的标签页
onMounted(() => {
  const savedActiveTab = localStorage.getItem('app_active_tab')
  if (savedActiveTab && tabs.value.some(tab => tab.path === savedActiveTab)) {
    activeTab.value = savedActiveTab
  }
})

// 监听激活标签页变化，保存到localStorage
watch(
  () => activeTab.value,
  (newActiveTab) => {
    localStorage.setItem('app_active_tab', newActiveTab)
  }
)

// 暴露方法给父组件使用
defineExpose({
  addTab,
  removeTab,
})
</script>

<style scoped>
.tabs-view {
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  padding: 0 20px;
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 4px;
}

.tab-icon {
  font-size: 14px;
}

:deep(.el-tabs__header) {
  margin: 0;
  border-bottom: none;
}

:deep(.el-tabs__nav-wrap) {
  padding: 8px 0;
}

:deep(.el-tabs__item) {
  border: 1px solid #d9d9d9;
  border-radius: 4px 4px 0 0;
  margin-right: 4px;
  padding: 0 16px;
  height: 32px;
  line-height: 30px;
  font-size: 12px;
  background: #f5f5f5;
  color: #666;
  transition: all 0.3s;
}

:deep(.el-tabs__item:hover) {
  background: #e6f7ff;
  color: #1890ff;
}

:deep(.el-tabs__item.is-active) {
  background: #fff;
  color: #1890ff;
  border-bottom-color: #fff;
  position: relative;
  z-index: 1;
}

:deep(.el-tabs__item .el-icon-close) {
  font-size: 12px;
  margin-left: 4px;
}

:deep(.el-tabs__content) {
  display: none;
}
</style>
