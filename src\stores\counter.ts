// src/stores/user.ts
import { ref } from 'vue'
import { defineStore } from 'pinia'
import {
  login,
  logout,
  getCurrentUser,
  type LoginDTO,
  type LoginVO,
  type User,
} from '@/api/system/user'
import { ElMessage } from 'element-plus'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref<string>(localStorage.getItem('token') || '')
  const userInfo = ref<User | null>(null)
  const isLoggedIn = ref<boolean>(!!token.value)

  // 初始化用户信息
  const initUserInfo = () => {
    const savedUserInfo = localStorage.getItem('userInfo')
    if (savedUserInfo) {
      try {
        userInfo.value = JSON.parse(savedUserInfo)
      } catch (error) {
        console.error('解析用户信息失败:', error)
        localStorage.removeItem('userInfo')
      }
    }
  }

  // 登录
  const userLogin = async (loginData: LoginDTO) => {
    try {
      const response = await login(loginData)
      if (response.code === 1) {
        const loginInfo = response.data
        // 保存token
        token.value = loginInfo.token
        localStorage.setItem('token', loginInfo.token)

        // 保存基本用户信息
        const basicUserInfo: User = {
          id: loginInfo.userId,
          username: loginInfo.username,
          password: '', // 不保存密码
          position: loginInfo.position,
          status: 'active',
          sortOrder: 0, // 默认排序
        }
        userInfo.value = basicUserInfo
        localStorage.setItem('userInfo', JSON.stringify(basicUserInfo))

        isLoggedIn.value = true
        ElMessage.success('登录成功')
        return true
      } else {
        ElMessage.error(response.msg || '登录失败')
        return false
      }
    } catch (error: any) {
      ElMessage.error(error.message || '登录失败')
      return false
    }
  }

  // 获取当前用户详细信息
  const fetchCurrentUser = async () => {
    try {
      const response = await getCurrentUser()
      if (response.code === 1) {
        userInfo.value = response.data
        localStorage.setItem('userInfo', JSON.stringify(response.data))
        return true
      } else {
        ElMessage.error(response.msg || '获取用户信息失败')
        return false
      }
    } catch (error: any) {
      ElMessage.error(error.message || '获取用户信息失败')
      return false
    }
  }

  // 登出
  const userLogout = async () => {
    try {
      await logout()
      // 清除本地存储
      token.value = ''
      userInfo.value = null
      isLoggedIn.value = false
      localStorage.removeItem('token')
      localStorage.removeItem('userInfo')

      // 清除权限数据
      const { usePermissionStore } = await import('./permission')
      const permissionStore = usePermissionStore()
      permissionStore.resetPermission()

      ElMessage.success('退出成功')
      return true
    } catch (error: any) {
      // 即使接口调用失败，也要清除本地存储
      token.value = ''
      userInfo.value = null
      isLoggedIn.value = false
      localStorage.removeItem('token')
      localStorage.removeItem('userInfo')

      // 清除权限数据
      const { usePermissionStore } = await import('./permission')
      const permissionStore = usePermissionStore()
      permissionStore.resetPermission()

      ElMessage.warning('退出登录')
      return true
    }
  }

  // 初始化
  initUserInfo()

  return {
    token,
    userInfo,
    isLoggedIn,
    userLogin,
    userLogout,
    fetchCurrentUser,
  }
})
