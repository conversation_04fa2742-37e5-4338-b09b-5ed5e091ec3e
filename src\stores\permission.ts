// src/stores/permission.ts
import { ref } from 'vue'
import { defineStore } from 'pinia'
import { getUserMenuTree, type SysMenu } from '@/api/system/user'
import { ElMessage } from 'element-plus'
import type { RouteRecordRaw } from 'vue-router'

// 动态导入组件的映射
const componentMap: Record<string, () => Promise<any>> = {
  // 首页
  Dashboard: () => import('@/views/Dashboard.vue'),

  // 系统管理
  MenuManagement: () => import('@/views/system/MenuManagement.vue'),
  UserManagement: () => import('@/views/system/UserManagement.vue'),
  SystemSettings: () => import('@/views/NotFound.vue'), // 暂时使用占位组件
  SystemLogs: () => import('@/views/NotFound.vue'), // 暂时使用占位组件

  // 销售管理
  ProductManagement: () => import('@/views/sale/product/ProductManagement.vue'),

  // 个人中心
  Profile: () => import('@/views/Profile.vue'),
}

export const usePermissionStore = defineStore('permission', () => {
  // 状态
  const userMenus = ref<SysMenu[]>([])
  const dynamicRoutes = ref<RouteRecordRaw[]>([])
  const isRoutesGenerated = ref(false)
  const showMenuRemarkTooltip = ref(true) // 控制菜单备注气泡框显示，默认开启

  // 设置菜单备注气泡框显示状态
  const setShowMenuRemarkTooltip = (show: boolean) => {
    showMenuRemarkTooltip.value = show
  }

  // 获取用户菜单
  const fetchUserMenus = async () => {
    try {
      const response = await getUserMenuTree()
      if (response.code === 1) {
        userMenus.value = response.data
        return true
      } else {
        ElMessage.error(response.msg || '获取菜单失败')
        return false
      }
    } catch (error: any) {
      ElMessage.error(error.message || '获取菜单失败')
      return false
    }
  }

  // 将菜单转换为路由
  const menuToRoute = (menu: SysMenu): RouteRecordRaw | null => {
    if (!menu.path) return null

    const route: any = {
      path: menu.path,
      name: menu.path.replace(/\//g, '-').replace(/^-/, ''),
      meta: {
        title: menu.menuName,
        icon: menu.icon,
        menuId: menu.id,
        requiresAuth: true,
        // 只有个人中心不在菜单中显示，首页和其他菜单都显示
        hideInMenu: menu.component === 'Profile',
      },
    }

    // 如果有组件路径，动态导入组件
    if (menu.component && componentMap[menu.component]) {
      route.component = componentMap[menu.component]
    } else if (menu.component) {
      // 如果组件不在映射中，使用默认组件
      console.warn(`Component not found: ${menu.component}, using NotFound component`)
      route.component = () => import('@/views/NotFound.vue')
    }

    // 处理子菜单
    if (menu.children && menu.children.length > 0) {
      route.children = []
      menu.children.forEach((child) => {
        const childRoute = menuToRoute(child)
        if (childRoute) {
          route.children!.push(childRoute)
        }
      })

      // 为有子菜单的父级路由添加redirect属性，重定向到第一个子路由
      if (route.children && route.children.length > 0) {
        route.redirect = route.children[0].path
      }
    }

    return route
  }

  // 生成动态路由
  const generateRoutes = () => {
    const routes: RouteRecordRaw[] = []

    userMenus.value.forEach((menu) => {
      const route = menuToRoute(menu)
      if (route) {
        routes.push(route)
      }
    })

    dynamicRoutes.value = routes
    isRoutesGenerated.value = true
    return routes
  }

  // 检查用户是否有某个菜单的权限
  const hasMenuPermission = (menuId: number): boolean => {
    const checkMenu = (menus: SysMenu[]): boolean => {
      for (const menu of menus) {
        if (menu.id === menuId) {
          return true
        }
        if (menu.children && menu.children.length > 0) {
          if (checkMenu(menu.children)) {
            return true
          }
        }
      }
      return false
    }
    return checkMenu(userMenus.value)
  }

  // 检查用户是否有某个路径的权限
  const hasPathPermission = (path: string): boolean => {
    const checkPath = (menus: SysMenu[]): boolean => {
      for (const menu of menus) {
        if (menu.path === path) {
          return true
        }
        if (menu.children && menu.children.length > 0) {
          if (checkPath(menu.children)) {
            return true
          }
        }
      }
      return false
    }
    return checkPath(userMenus.value)
  }

  // 获取面包屑导航
  const getBreadcrumb = (path: string): Array<{ title: string; path?: string }> => {
    const breadcrumb: Array<{ title: string; path?: string }> = []

    const findPath = (menus: SysMenu[], targetPath: string, parents: SysMenu[] = []): boolean => {
      for (const menu of menus) {
        const currentPath = [...parents, menu]

        if (menu.path === targetPath) {
          // 找到目标路径，构建面包屑
          currentPath.forEach((item) => {
            breadcrumb.push({
              title: item.menuName,
              path: item.path,
            })
          })
          return true
        }

        if (menu.children && menu.children.length > 0) {
          if (findPath(menu.children, targetPath, currentPath)) {
            return true
          }
        }
      }
      return false
    }

    findPath(userMenus.value, path)
    return breadcrumb
  }

  // 重置权限数据
  const resetPermission = () => {
    userMenus.value = []
    dynamicRoutes.value = []
    isRoutesGenerated.value = false
  }

  // 初始化权限数据
  const initPermission = async () => {
    const success = await fetchUserMenus()
    if (success) {
      generateRoutes()
    }
    return success
  }

  return {
    userMenus,
    dynamicRoutes,
    isRoutesGenerated,
    showMenuRemarkTooltip,
    setShowMenuRemarkTooltip,
    fetchUserMenus,
    generateRoutes,
    hasMenuPermission,
    hasPathPermission,
    getBreadcrumb,
    resetPermission,
    initPermission,
  }
})
