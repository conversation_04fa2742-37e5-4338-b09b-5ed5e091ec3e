/**
 * 树形表格排序工具
 * 专门处理树形结构拖拽后的排序逻辑，支持同级排序
 * use context7
 */

import type { SysMenu } from '@/api/system/user'

/**
 * 树形排序配置接口
 */
export interface TreeSortConfig {
  /** 树形数据数组 */
  treeData: SysMenu[]
  /** 拖拽的菜单项 */
  draggedItem: SysMenu
  /** 目标位置的菜单项 */
  targetItem: SysMenu
  /** 原始索引 */
  oldIndex: number
  /** 新索引 */
  newIndex: number
}

/**
 * 获取同级的所有菜单项（不包括子菜单）
 * @param treeData 完整的树形数据
 * @param parentId 父级ID
 * @returns 同级菜单项数组
 */
const getSiblings = (treeData: SysMenu[], parentId: number): SysMenu[] => {
  const siblings: SysMenu[] = []

  // 递归查找指定父级下的直接子菜单
  const findDirectChildren = (items: SysMenu[], targetParentId: number): SysMenu[] => {
    const result: SysMenu[] = []
    for (const item of items) {
      if (item.parentId === targetParentId) {
        result.push(item)
      }
      // 递归查找子菜单中的直接子项
      if (item.children && item.children.length > 0) {
        result.push(...findDirectChildren(item.children, targetParentId))
      }
    }
    return result
  }

  return findDirectChildren(treeData, parentId)
}

/**
 * 重新排序同级菜单项
 * @param siblings 同级菜单项数组
 * @param draggedItem 被拖拽的菜单项
 * @param targetIndex 目标位置索引
 * @returns 重新排序后的同级菜单项数组
 */
const reorderSiblings = (
  siblings: SysMenu[],
  draggedItem: SysMenu,
  targetIndex: number,
): SysMenu[] => {
  // 移除被拖拽的项
  const filteredSiblings = siblings.filter((item) => item.id !== draggedItem.id)

  // 在目标位置插入被拖拽的项
  const reorderedSiblings = [...filteredSiblings]
  reorderedSiblings.splice(targetIndex, 0, draggedItem)

  return reorderedSiblings
}

/**
 * 更新树形数据的排序值
 * @param treeData 完整的树形数据
 * @param parentId 父级ID
 * @param reorderedSiblings 重新排序后的同级菜单项
 * @returns 更新后的树形数据
 */
const updateTreeSortOrder = (
  treeData: SysMenu[],
  parentId: number,
  reorderedSiblings: SysMenu[],
): SysMenu[] => {
  const updateRecursive = (items: SysMenu[]): SysMenu[] => {
    return items.map((item) => {
      if (item.parentId === parentId) {
        // 找到对应的排序后的菜单项
        const reorderedItem = reorderedSiblings.find((s) => s.id === item.id)
        if (reorderedItem) {
          return {
            ...item,
            sortOrder: reorderedSiblings.findIndex((s) => s.id === item.id),
          }
        }
      }

      // 递归处理子菜单
      if (item.children && item.children.length > 0) {
        return {
          ...item,
          children: updateRecursive(item.children),
        }
      }

      return item
    })
  }

  return updateRecursive(treeData)
}

/**
 * 获取需要更新的菜单项列表（只包含需要更新的项）
 * @param reorderedSiblings 重新排序后的同级菜单项
 * @returns 需要更新的菜单项数组
 */
const getItemsToUpdate = (reorderedSiblings: SysMenu[]): SysMenu[] => {
  return reorderedSiblings.map((item, index) => ({
    ...item,
    sortOrder: index,
  }))
}

/**
 * 处理树形表格拖拽后的排序
 * @param config 排序配置
 * @returns 排序结果，包含更新后的树形数据和需要API更新的菜单项列表
 */
export const handleTreeTableSort = (config: TreeSortConfig) => {
  const { treeData, draggedItem, targetItem, oldIndex, newIndex } = config

  try {
    // 获取同级的所有菜单项
    const siblings = getSiblings(treeData, draggedItem.parentId)

    // 找到被拖拽项在同级中的原始位置
    const draggedOriginalIndex = siblings.findIndex((item) => item.id === draggedItem.id)
    if (draggedOriginalIndex === -1) {
      throw new Error('未找到被拖拽的菜单项')
    }

    // 找到目标项在同级中的位置
    const targetOriginalIndex = siblings.findIndex((item) => item.id === targetItem.id)
    if (targetOriginalIndex === -1) {
      throw new Error('未找到目标菜单项')
    }

    // 计算在同级中的新位置
    let newSiblingIndex: number
    if (newIndex > oldIndex) {
      // 向下拖拽，目标位置应该是targetOriginalIndex
      newSiblingIndex = targetOriginalIndex
    } else {
      // 向上拖拽，目标位置应该是targetOriginalIndex
      newSiblingIndex = targetOriginalIndex
    }

    // 确保索引在有效范围内
    newSiblingIndex = Math.max(0, Math.min(newSiblingIndex, siblings.length - 1))

    // 重新排序同级菜单项
    const reorderedSiblings = reorderSiblings(siblings, draggedItem, newSiblingIndex)

    // 更新树形数据的排序值
    const updatedTreeData = updateTreeSortOrder(treeData, draggedItem.parentId, reorderedSiblings)

    // 获取需要API更新的菜单项
    const itemsToUpdate = getItemsToUpdate(reorderedSiblings)
    return {
      success: true,
      message: '排序成功',
      updatedTreeData,
      itemsToUpdate,
    }
  } catch (error) {
    console.error('树形表格排序失败:', error)
    return {
      success: false,
      message: error instanceof Error ? error.message : '排序处理失败',
      updatedTreeData: treeData,
      itemsToUpdate: [],
    }
  }
}
