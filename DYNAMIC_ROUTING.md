# 动态路由设计说明

## 概述

本ERP系统采用动态路由设计，路由配置存储在数据库中，前端根据用户权限动态生成路由表。这样可以实现灵活的权限控制和菜单管理。

## 路由映射机制

### 1. 数据库菜单表结构

```sql
CREATE TABLE `sys_menus` (
  `id` int PRIMARY KEY AUTO_INCREMENT,
  `parent_id` int DEFAULT 0,
  `menu_name` varchar(50) NOT NULL,
  `icon` varchar(50),
  `path` varchar(200),           -- 路由路径，如 /system/users
  `component` varchar(100),      -- 组件名称，如 UserManagement
  `sort_order` int DEFAULT 0
);
```

### 2. 路径到组件的映射规则

#### A. 组件命名约定
- **路由路径**: `/system/users`
- **组件名称**: `UserManagement`
- **文件位置**: `src/views/system/UserManagement.vue`

#### B. 映射逻辑 (`src/router/index.ts`)

```typescript
// 动态导入组件的映射表
const componentMap: Record<string, () => Promise<any>> = {
  // 首页
  'Dashboard': () => import('@/views/Dashboard.vue'),
  
  // 系统管理
  'MenuManagement': () => import('@/views/system/MenuManagement.vue'),
  'UserManagement': () => import('@/views/system/UserManagement.vue'),
  'SystemSettings': () => import('@/views/system/SystemSettings.vue'),
  'SystemLogs': () => import('@/views/system/SystemLogs.vue'),
  
  // 业务管理
  'OrderManagement': () => import('@/views/business/OrderManagement.vue'),
  'OrderList': () => import('@/views/business/OrderList.vue'),
  'OrderStatistics': () => import('@/views/business/OrderStatistics.vue'),
  'CustomerManagement': () => import('@/views/business/CustomerManagement.vue'),
  'ProductManagement': () => import('@/views/business/ProductManagement.vue'),
  'InventoryManagement': () => import('@/views/business/InventoryManagement.vue'),
  
  // 报表中心
  'SalesReport': () => import('@/views/reports/SalesReport.vue'),
  'FinanceReport': () => import('@/views/reports/FinanceReport.vue'),
  'InventoryReport': () => import('@/views/reports/InventoryReport.vue'),
  
  // 个人中心
  'Profile': () => import('@/views/Profile.vue'),
}
```

### 3. 动态路由生成过程

#### A. 用户登录后获取菜单权限

```typescript
// 1. 用户登录成功后，获取用户菜单权限
const userMenus = await getUserMenuTree()

// 2. 根据菜单数据生成路由
const routes = generateRoutesFromMenus(userMenus)

// 3. 动态添加路由到路由器
routes.forEach(route => {
  router.addRoute('MainLayout', route)
})
```

#### B. 路由生成函数

```typescript
function generateRoutesFromMenus(menus: SysMenu[]): RouteRecordRaw[] {
  const routes: RouteRecordRaw[] = []
  
  menus.forEach(menu => {
    if (menu.path && menu.component) {
      const route: RouteRecordRaw = {
        path: menu.path,                    // 如: /system/users
        name: menu.component,               // 如: UserManagement
        component: componentMap[menu.component], // 动态导入组件
        meta: {
          title: menu.menuName,             // 菜单标题
          icon: menu.icon,                  // 图标
          requiresAuth: true,               // 需要认证
        }
      }
      routes.push(route)
    }
    
    // 递归处理子菜单
    if (menu.children && menu.children.length > 0) {
      routes.push(...generateRoutesFromMenus(menu.children))
    }
  })
  
  return routes
}
```

## 具体示例

### 示例1: 用户管理页面

1. **数据库记录**:
   ```sql
   INSERT INTO sys_menus (id, parent_id, menu_name, icon, path, component, sort_order) 
   VALUES (102, 10, '用户管理', 'UserFilled', '/system/users', 'UserManagement', 2);
   ```

2. **路由生成**:
   ```typescript
   {
     path: '/system/users',
     name: 'UserManagement',
     component: () => import('@/views/system/UserManagement.vue'),
     meta: {
       title: '用户管理',
       icon: 'UserFilled',
       requiresAuth: true
     }
   }
   ```

3. **访问流程**:
   - 用户点击菜单 → 路由跳转到 `/system/users`
   - Vue Router 匹配路由 → 加载 `UserManagement.vue` 组件
   - 组件渲染 → 显示用户管理页面

### 示例2: 订单统计页面

1. **数据库记录**:
   ```sql
   INSERT INTO sys_menus (id, parent_id, menu_name, icon, path, component, sort_order) 
   VALUES (2012, 201, '订单统计', 'DataAnalysis', '/business/orders/statistics', 'OrderStatistics', 2);
   ```

2. **文件结构**:
   ```
   src/views/business/OrderStatistics.vue
   ```

3. **组件映射**:
   ```typescript
   'OrderStatistics': () => import('@/views/business/OrderStatistics.vue')
   ```

## 权限控制机制

### 1. 菜单权限控制

```typescript
// 用户只能看到有权限的菜单
const filteredMenus = computed(() => {
  return permissionStore.userMenus.filter(menu => {
    return permissionStore.hasMenuPermission(menu.id)
  })
})
```

### 2. 路由权限控制

```typescript
// 路由守卫检查权限
router.beforeEach((to, from, next) => {
  if (to.meta.requiresAuth) {
    if (permissionStore.hasRoutePermission(to.path)) {
      next()
    } else {
      next('/403') // 无权限页面
    }
  } else {
    next()
  }
})
```

## 添加新页面的步骤

### 1. 创建Vue组件文件

```bash
# 在对应目录下创建组件文件
src/views/system/NewFeature.vue
```

### 2. 在componentMap中注册组件

```typescript
const componentMap: Record<string, () => Promise<any>> = {
  // ... 其他组件
  'NewFeature': () => import('@/views/system/NewFeature.vue'),
}
```

### 3. 在数据库中添加菜单记录

```sql
INSERT INTO sys_menus (parent_id, menu_name, icon, path, component, sort_order) 
VALUES (10, '新功能', 'Star', '/system/new-feature', 'NewFeature', 5);
```

### 4. 为用户分配权限

```sql
INSERT INTO sys_user_menu (user_id, menu_id) 
VALUES (1, LAST_INSERT_ID());
```

## 优势

1. **灵活性**: 可以通过数据库动态配置菜单和路由
2. **权限控制**: 精确控制用户可访问的页面
3. **可维护性**: 新增页面只需要添加组件和数据库记录
4. **安全性**: 前端路由和后端权限双重验证

## 注意事项

1. **组件名称**: 必须在 `componentMap` 中注册
2. **路径唯一性**: 每个路由路径必须唯一
3. **权限同步**: 前端路由权限要与后端API权限保持一致
4. **懒加载**: 使用动态导入实现组件懒加载，提高性能

## 调试技巧

1. **查看生成的路由**:
   ```typescript
   console.log('Generated routes:', router.getRoutes())
   ```

2. **检查用户权限**:
   ```typescript
   console.log('User menus:', permissionStore.userMenus)
   ```

3. **验证组件映射**:
   ```typescript
   console.log('Component map:', componentMap)
   ```
